{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=8.1", "workerman/webman-framework": "^2.1", "monolog/monolog": "^2.0", "webman/console": "^2.1", "workerman/crontab": "^1.0", "webman/redis": "^2.1", "illuminate/events": "^11.44", "webman/redis-queue": "^2.1", "workerman/channel": "^1.2", "vlucas/phpdotenv": "^5.6", "webman/admin": "^2.0", "webman/openai": "^1.0", "guzzlehttp/guzzle": "^7.9", "ext-pdo": "*", "webman/cache": "^2.1", "aliyuncs/oss-sdk-php": "^2.7", "tinywan/jwt": "^1.11", "workerman/workerman": "^5.1", "webman/event": "^1.0", "webman/rate-limiter": "^1.1", "robmorgan/phinx": "^0.16.6"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components"}}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}, "minimum-stability": "dev", "prefer-stable": true, "require-dev": {"phpunit/phpunit": "^11.5"}}