<?php
return [
    # 默认消费者
    'consumer'  => [
        'handler'     => Webman\RedisQueue\Process\Consumer::class,
        'count'       => 8, // 可以设置多进程同时消费
        'constructor' => [
            // 消费者类目录
            'consumer_dir' => app_path() . '/queue/redis'
        ]
    ],
    # 音频生成消费者
    'audio_gen_task'  => [
        'handler'     => Webman\RedisQueue\Process\Consumer::class,
        'count'       => 10, // 可以设置多进程同时消费
        'constructor' => [
            // 消费者类目录
            'consumer_dir' => app_path() . '/queue/redis/audio'
        ]
    ],
    # 预设对话消费者
    'preset_dialogues'  => [
        'handler'     => Webman\RedisQueue\Process\Consumer::class,
        'count'       => 10, // 可以设置多进程同时消费
        'constructor' => [
            // 消费者类目录
            'consumer_dir' => app_path() . '/queue/redis/preset'
        ]
    ],
    # 句子更新消费者
    'update-sentences'  => [
        'handler'     => Webman\RedisQueue\Process\Consumer::class,
        'count'       => 10, // 可以设置多进程同时消费
        'constructor' => [
            // 消费者类目录
            'consumer_dir' => app_path() . '/queue/redis/update'
        ]
    ],
    # 对话生成消费者
    'create_dialogues'  => [
        'handler'     => Webman\RedisQueue\Process\Consumer::class,
        'count'       => 10, // 可以设置多进程同时消费
        'constructor' => [
            // 消费者类目录
            'consumer_dir' => app_path() . '/queue/redis/create'
        ]
    ],
];
