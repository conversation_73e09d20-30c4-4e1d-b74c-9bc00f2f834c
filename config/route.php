<?php

use app\controller\AiController;
use plugin\admin\app\controller\IndexController;
use Webman\Route;

# ai
Route::group('/ai', function () {
    Route::get('/completions', [AiController::class, 'completions']);
});

Route::any('/kkdsgjhnxmcng', [IndexController::class, 'index']);

# 测试环境路由（无前缀）
# 首页（测试版本）
Route::get('/', [\app\controller\IndexController::class, 'home']);

# 对话质量检查相关路由（测试版本）
Route::get('/quality', [\app\controller\IndexController::class, 'quality']);
Route::get('/topics', [\app\controller\IndexController::class, 'topics']);
Route::get('/random', [\app\controller\IndexController::class, 'random']);

# 对话生成测试相关路由（测试版本）
Route::get('/generator', [\app\controller\IndexController::class, 'generator']);
Route::post('/generateTest', [\app\controller\IndexController::class, 'generateTest']);
Route::get('/getPrompts', [\app\controller\IndexController::class, 'getPrompts']);
