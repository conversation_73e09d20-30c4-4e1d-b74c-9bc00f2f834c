# AI教师

## 简介
AI教师是一个基于 webman 框架开发的后端项目，旨在通过AI技术提升教育体验。

## 技术栈
- **PHP 8.2**: 运行环境
- **Composer**: PHP 的依赖管理工具。
- **webman**: 高性能的 PHP 开发框架。
- **MySQL**
- **Redis**: 用于缓存和消息队列的高性能存储。
- **JWT**: 用于安全的用户身份验证。

## 目录
- [安装](#安装)
- [配置](#配置)
- [使用说明](#使用说明)
- [第三方服务](#第三方服务)

## 安装
1. **克隆仓库**:
    ```bash
    git clone https://gitee.com/fm210/ai-teacher.git
    cd ai-teacher
    ```

2. **使用 Composer 安装依赖**:
    ```bash
    composer install
    ```

## 配置
1. **环境文件设置**:
    ```bash
    cp .env.example .env
    ```

2. **编辑 `.env` 文件配置环境变量**:
    ```plaintext
    # 数据库配置
    DB_HOST=localhost
    DB_PORT=3306
    DB_USERNAME=root
    DB_PASSWORD=password
    DB_DATABASE=ai_teacher
    
    # 讯飞API配置
    XF_APP_ID=your_app_id
    XF_API_KEY=your_api_key
    XF_API_SECRET=your_api_secret
    
    # AI模型配置
    AI_API=http://api.example.com
    AI_API_KEY=your_ai_api_key
    AI_MODEL=Doubao-1.5-pro-32k
    ```

## 使用说明
1. **启动服务器**:
    ```bash
    php webman start
    ```

2. **访问应用**:
   访问 [http://localhost:9696](http://localhost:9696) 查看应用程序。

## 一键部署项目（docker）
```bash
chmod +x start.sh stop.sh
./start.sh
```

## 一键更新项目 (docker)
```bash
cd 项目目录
git pull
# 拉取最新版本成功后执行
./update.sh
```

## 第三方服务
- **讯飞技术**:
    - 翻译
    - 语音识别
    - 语音合成：支持自定义音色，当前配置为女声 `x4_lingxiaolu_em_v2` 和男声 `x4_lingfeizhe_emo`
    - 语音评测 suntone
- **火山引擎 (豆包模型)**: 使用模型 `Doubao-1.5-pro-32k` 或自定义智能体来增强 AI 功能。
