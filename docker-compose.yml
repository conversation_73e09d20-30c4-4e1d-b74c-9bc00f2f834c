volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  ait_network:
    driver: bridge

services:
  mysql:
    image: mysql:8.0.34
    container_name: ait_mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ait
      MYSQL_USER: ait
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - ./data/mysql:/var/lib/mysql
    networks:
      - ait_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 5s
      timeout: 3s
      retries: 3
    restart: unless-stopped

  redis:
    image: redis:6.2.11
    container_name: ait_redis
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}"]
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
    networks:
      - ait_network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 5s
      timeout: 3s
      retries: 3
    restart: unless-stopped

#  webman:
#    build: .
#    container_name: ait_server
#    ports:
#      - "9696:9696"
#    volumes:
##      - ./data/logs:/var/www/runtime/logs
#        - .:/var/www
#    depends_on:
#      mysql:
#        condition: service_healthy
#      redis:
#        condition: service_healthy
#    networks:
#      - ait_network
#    healthcheck:
#      test: ["CMD", "curl", "-f", "http://localhost:9696/health"]
#      interval: 10s
#      timeout: 5s
#      retries: 5
#    restart: unless-stopped
