<?php
use PHPUnit\Framework\TestCase;

class TestConfig extends TestCase
{
    public function testAppConfig()
    {
        $config = config('app');
        self::assertIsArray($config);
        self::assertArray<PERSON>as<PERSON>ey('debug', $config);
        self::assertIsBool($config['debug']);
        self::assertArray<PERSON>asKey('default_timezone', $config);
        self::assertIsString($config['default_timezone']);
    }
}