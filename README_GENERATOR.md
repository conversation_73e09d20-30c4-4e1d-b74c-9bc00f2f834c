# AI对话内容生成测试系统

## 功能概述

这是一个专业的AI对话内容生成测试系统，支持多种AI模型切换和对话模式，用于测试和评估AI生成对话的质量。

## 主要功能

### 1. 对话质量检查 (`/quality`)
- 随机展示10组预设对话
- 支持话题和难度筛选
- 质量评估与统计功能
- 响应式设计，支持移动端

### 2. 内容生成测试 (`/generator`)
- **模式选择**: 场景跟读 vs 自由对话
- **AI模型切换**: GPT-4o, GPT-4o-mini, 豆包等
- **话题配置**: 预设话题或自定义话题
- **难度调节**: 简单、中等、困难
- **演示模式**: 无需AI服务即可测试功能
- **质量评估**: 5星评分系统

## 访问地址

- **首页导航**: `http://localhost:9696/`
- **对话质量检查**: `http://localhost:9696/quality`
- **内容生成测试**: `http://localhost:9696/generator`

## 使用方法

### 内容生成测试

1. **访问生成测试页面**: `http://localhost:9696/generator`

2. **配置参数**:
   - 选择学习模式（场景跟读/自由对话）
   - 选择AI模型（GPT-4o/GPT-4o-mini/豆包等）
   - 设置难度等级（简单/中等/困难）
   - 选择预设话题或输入自定义话题

3. **生成对话**:
   - **演示模式**: 点击"🎭 演示模式"按钮，无需AI服务即可测试
   - **真实生成**: 点击"真实AI生成"按钮，使用配置的AI服务

4. **查看结果**:
   - 切换查看"解析结果"或"原始内容"
   - 对生成的对话进行质量评分（1-5星）
   - 添加评估备注

### 对话质量检查

1. **访问质量检查页面**: `http://localhost:9696/quality`
2. **选择话题和难度**
3. **点击"加载对话"**
4. **为每组对话评分**（优秀/一般/较差）
5. **查看统计结果**

## 技术特色

### 前端技术
- **Tailwind CSS**: 现代化响应式设计
- **原生JavaScript**: 无框架依赖，轻量高效
- **异步请求**: 使用Axios处理API调用
- **动画效果**: 流畅的用户交互体验

### 后端技术
- **PHP + Webman**: 高性能Web框架
- **异步AI调用**: 支持流式和非流式响应
- **多模型支持**: 可配置不同的AI服务提供商
- **演示模式**: 内置演示数据，无需依赖外部服务

### 数据结构
```json
{
  "success": true,
  "data": {
    "raw_content": "原始AI返回内容",
    "dialogues": [
      {
        "sort_order": 0,
        "role": "system",
        "content_zh": "中文对话内容",
        "content_vi": "越南语翻译"
      }
    ],
    "mode": "scene",
    "model": "gpt-4o-mini",
    "topic": "导游中文",
    "difficulty": "easy"
  }
}
```

## 演示模式

演示模式提供了丰富的预设对话内容，支持：

### 话题类型
- **导游中文**: 旅游场景对话
- **商务中文**: 商务洽谈对话
- **工厂中文**: 工厂参观对话

### 对话模式
- **场景跟读**: 结构化的教学对话
- **自由对话**: 更自然的交流形式

## 配置说明

### AI服务配置
在 `.env` 文件中配置AI服务：
```env
AI_API=https://api.openai.com/v1/chat/completions
AI_API_KEY=your_api_key_here
AI_MODEL=gpt-4o-mini
```

### 支持的AI模型
- `gpt-4o`: OpenAI GPT-4o
- `gpt-4o-mini`: OpenAI GPT-4o-mini
- `doubao-1-5-pro-32k-250115`: 字节跳动豆包
- `gpt-3.5-turbo`: OpenAI GPT-3.5

## 错误处理

系统提供完善的错误处理机制：

1. **AI服务未配置**: 自动提示使用演示模式
2. **生成超时**: 30秒超时保护
3. **网络错误**: 友好的错误提示
4. **参数验证**: 前后端双重验证

## 开发说明

### 文件结构
```
app/
├── controller/IndexController.php  # 主控制器
├── view/
│   ├── index.html                 # 首页导航
│   └── dialogue/
│       ├── quality.html           # 质量检查页面
│       └── generator.html         # 生成测试页面
└── service/
    └── Ai.php                     # AI服务类

config/
└── route.php                      # 路由配置
```

### API接口
- `GET /topics` - 获取话题列表
- `GET /random` - 获取随机对话
- `POST /generateTest` - 生成测试对话
- `GET /getPrompts` - 获取提示词模板

## 注意事项

1. **演示模式优先**: 建议先使用演示模式测试功能
2. **AI配置检查**: 确保AI服务配置正确后再使用真实生成
3. **超时设置**: AI生成有30秒超时限制
4. **浏览器兼容**: 建议使用现代浏览器（Chrome, Firefox, Safari）

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多种AI模型切换
- 实现演示模式
- 完整的质量评估系统
- 响应式UI设计
