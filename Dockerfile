# ============= 构建阶段 =============
FROM php:8.2-cli-alpine AS builder

# 1. 使用国内镜像源加速
RUN echo "https://mirrors.aliyun.com/alpine/v3.21/main/" > /etc/apk/repositories && \
    echo "https://mirrors.aliyun.com/alpine/v3.21/community/" >> /etc/apk/repositories

# 2. 安装编译依赖（分离运行时依赖）
RUN apk update && \
    apk add --no-cache --virtual .build-deps \
        autoconf \
        g++ \
        make \
        libpng-dev \
        libjpeg-turbo-dev \
        freetype-dev \
        libzip-dev \
        git \
        unzip

# 3. 安装PHP扩展（启用OPcache加速）
RUN docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install -j$(nproc) gd pdo_mysql pcntl zip opcache && \
    pecl install redis && \
    docker-php-ext-enable redis

# 4. 安装Composer（固定版本）
COPY --from=composer:2.7 /usr/bin/composer /usr/bin/composer

# 5. 优化依赖安装（利用缓存层）
WORKDIR /var/www
COPY composer.* ./
RUN composer install --no-dev --optimize-autoloader --no-scripts && \
    composer clear-cache

# ============= 运行阶段 =============
FROM php:8.2-cli-alpine

# 1. 仅安装运行时依赖
RUN echo "https://mirrors.aliyun.com/alpine/v3.21/main/" > /etc/apk/repositories && \
    echo "https://mirrors.aliyun.com/alpine/v3.21/community/" >> /etc/apk/repositories && \
    apk update && \
    apk add --no-cache \
        libpng \
        libjpeg-turbo \
        freetype \
        libzip

# 2. 从构建阶段复制编译成果
COPY --from=builder /usr/local/etc/php/conf.d/ /usr/local/etc/php/conf.d/
COPY --from=builder /usr/local/lib/php/extensions/no-debug-non-zts-20220829/ /usr/local/lib/php/extensions/no-debug-non-zts-20220829/
COPY --from=builder /var/www/vendor /var/www/vendor

# 3. 创建非root用户
RUN adduser -D -u 1000 webman && \
    mkdir -p /var/www/runtime && \
    chown -R webman:webman /var/www

# 4. 复制应用代码（最后一步最大化利用缓存）
WORKDIR /var/www
COPY --chown=webman:webman . .

# 5. 优化PHP配置
RUN echo "opcache.enable=1" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.memory_consumption=128" >> /usr/local/etc/php/conf.d/opcache.ini && \
    echo "opcache.max_accelerated_files=10000" >> /usr/local/etc/php/conf.d/opcache.ini

# 6. 运行时配置
USER webman
EXPOSE 9696
HEALTHCHECK --interval=30s --timeout=5s \
    CMD curl -fsS http://localhost:9696/health || exit 1
CMD ["php", "webman", "start"]