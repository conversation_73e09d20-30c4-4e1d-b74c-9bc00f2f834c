<?php

namespace app\model;

use support\Model;

/**
 * user_preferences 用户偏好设置
 * @property integer $user_id 
 * @property string $teacher_gender 教师性别
 * @property string $speech_speed 语速倍数
 */
class UserPreferences extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_preferences';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    # 隐藏字段
    protected $hidden = ['user_id'];
    # 关联表
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
}
