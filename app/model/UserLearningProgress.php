<?php

namespace app\model;

use support\Model;

/**
 * user_learning_progress 用户学习进度表，区分不同学习模式的进度
 * @property integer $id 进度记录唯一标识(主键)
 * @property integer $user_id 用户ID
 * @property integer $topic_id 话题ID
 * @property string $learning_mode 学习模式
 * @property integer $last_sentence_id 最近学习的句子ID（自由对话模式时可为空）
 * @property integer $completed 话题学习是否完成
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class UserLearningProgress extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_learning_progress';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    
}
