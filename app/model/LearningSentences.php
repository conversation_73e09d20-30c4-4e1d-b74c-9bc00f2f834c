<?php

namespace app\model;

use support\Model;

/**
 * sentences 示范朗读句子库
 * @property integer $sentence_id (主键)
 * @property integer $topic_id 
 * @property string $content_zh 中文内容
 * @property string $content_vi 越南语翻译
 * @property integer $sort_order 排序位置
 */
class LearningSentences extends Model
{
    protected $fillable = ['sentence_id', 'topic_id', 'content_zh', 'content_vi'];
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'learning_sentences';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'sentence_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    #一对多
    public function topic()
    {
        return $this->belongsTo(LearningTopics::class, 'topic_id', 'topic_id');
    }
    #一对多
    public function audios()
    {
        return $this->hasMany(SentenceAudioResources::class, 'sentence_id', 'sentence_id');
    }
    
}
