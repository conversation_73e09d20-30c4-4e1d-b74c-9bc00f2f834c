<?php

namespace app\model;

use support\Model;

/**
 * users 用户表
 * @property integer $user_id 用户唯一标识(主键)
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $zalo_openid Zalo开放平台ID
 * @property string $nickname 用户昵称
 * @property string $avatar_url 头像地址
 * @property integer $is_first_login 首次登录标识
 */
class User extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    protected $fillable = [
        'zalo_openid',
        'nickname',
        'avatar_url',
        'is_first_login',
    ];
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    protected static function booted(): void
    {
        static::created(function ($model) {
            $userPreferences = new UserPreferences();
            $userPreferences->user_id = $model->user_id;
            $userPreferences->save();
        });
        # 删除用户偏好设置
        static::deleting(function ($model) {
            $model->userPreferences()->delete();
        });
    }
    # 隐藏字段
    protected $hidden = ['user_id'];
    # 关联表
    public function userPreferences()
    {
        return $this->hasOne(UserPreferences::class, 'user_id', 'user_id');
    }
    public function userSentenceRecord()
    {
        return $this->hasMany(UserSentenceRecord::class, 'user_id', 'user_id');
    }
    # 学习记录
    public function userLearningProgress()
    {
        return $this->hasMany(UserLearningProgress::class, 'user_id', 'user_id');
    }

    # 评测记录
    public function userPronunciationRecord()
    {
        return $this->hasMany(UserPronunciationRecord::class, 'user_id', 'user_id');
    }


    
    
}
