<?php

namespace app\model;

use support\Model;

/**
 * scene_dialogue_audio_resources 场景跟读对话句子音频资源
 * @property integer $audio_id 音频唯一标识ID(主键)
 * @property integer $message_id 场景对话句子ID
 * @property string $teacher_gender 教师性别音色选择
 * @property string $speed 语速倍率（slow-0.8, standard-1, fast-1.2）
 * @property string $audio_url 音频资源文件路径
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class SceneDialogueAudioResources extends Model
{
    protected $fillable = ['message_id', 'teacher_gender', 'speed', 'audio_url'];
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'scene_dialogue_audio_resources';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'audio_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    # audio_url补全
    public function getAudioUrlAttribute($value): string
    {
        return 'https://english-oversea.nofbcdn.cn/'.$value;
    }

    # 一对多
    public function message()
    {
        return $this->belongsTo(UserSceneDialogueMessages::class, 'message_id', 'message_id');
    }
    
}
