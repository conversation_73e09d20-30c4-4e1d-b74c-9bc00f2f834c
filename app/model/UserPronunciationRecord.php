<?php

namespace app\model;

use support\Model;

/**
 * user_pronunciation_record 用户已学习发音评测句子记录
 * @property integer $id (主键)
 * @property integer $user_id 用户ID
 * @property integer $sentence_id 句子ID
 * @property string $created_at 学习时间
 * @property string $updated_at
 */
class UserPronunciationRecord extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_pronunciation_record';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
}
