<?php

namespace app\model;

use support\Model;

/**
 * user_scene_dialogue_sessions 用户场景对话会话记录（支持预设与自定义话题）
 * @property integer $session_id 场景对话会话ID(主键)
 * @property integer $user_id 用户ID
 * @property integer $topic_id 预设话题ID（若为自定义则为空）
 * @property string $custom_topic_zh 自定义话题中文内容（预设话题为空）
 * @property string $custom_topic_vi 自定义话题越南语内容（预设话题为空）
 * @property string $learning_mode 学习模式
 * @property string $difficulty 难易程度
 * @property string $status 会话状态
 * @property string $created_at 
 * @property string $updated_at
 */
class UserSceneDialogueSessions extends Model
{
    protected $fillable = ['session_id', 'user_id', 'topic_id', 'custom_topic_zh', 'custom_topic_vi', 'learning_mode', 'difficulty', 'status', 'created_at', 'updated_at'];
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_scene_dialogue_sessions';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'session_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    # 一对多
    public function messages()
    {
        return $this->hasMany(UserSceneDialogueMessages::class, 'session_id', 'session_id');
    }

    
    
}
