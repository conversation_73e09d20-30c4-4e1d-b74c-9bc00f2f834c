<?php

namespace app\model;

use support\Model;

/**
 * ai_prompts ai提示词
 * @property integer $prompt_id 主键(主键)
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property string $module 模块名称
 * @property string $prompt_template 提示词模板内容	
 * @property string $available_params 参数列表
 * @property string $desc 描述
 */
class AiPromptTemplates extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ai_prompt_templates';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'prompt_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;
    
    
}
