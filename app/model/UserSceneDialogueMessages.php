<?php

namespace app\model;

use support\Model;

/**
 * user_scene_dialogue_messages 用户场景对话单句消息记录（带排序）
 * @property integer $message_id 对话消息ID(主键)
 * @property integer $session_id 所属会话ID
 * @property string $role 角色
 * @property string $sender 发送者角色
 * @property integer $sort_order 对话顺序（从1开始递增）
 * @property string $content_zh 中文对话内容
 * @property string $content_vi 越南语翻译内容
 * @property string $created_at
 */
class UserSceneDialogueMessages extends Model
{
    protected $fillable = ['message_id', 'session_id','role', 'sender', 'sort_order', 'content_zh', 'content_vi', 'created_at'];
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_scene_dialogue_messages';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'message_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    # 一对多
    public function session()
    {
        return $this->belongsTo(UserSceneDialogueSessions::class, 'session_id', 'session_id');
    }

    # 一对多
    public function audios()
    {
        return $this->hasMany(SceneDialogueAudioResources::class, 'message_id', 'message_id');
    }


    
}
