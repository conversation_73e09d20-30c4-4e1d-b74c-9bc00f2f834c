<?php

namespace app\model;

use support\Model;

/**
 * sentence_audios 示范朗读音频资源
 * @property integer $audio_id 音频唯一标识ID(主键)
 * @property integer $sentence_id 句子ID
 * @property string $teacher_gender 教师性别音色选择
 * @property string $speed 语速倍率，如0.8（较慢）, 1（标准）, 1.2（较快）
 * @property string $audio_url 音频资源文件路径
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class SentenceAudioResources extends Model
{

    protected $fillable = ['sentence_id', 'teacher_gender', 'speed', 'audio_url'];
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sentence_audio_resources';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'audio_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;


    # audio_url补全
    public function getAudioUrlAttribute($value): string
    {
        return 'https://english-oversea.nofbcdn.cn/'.$value;
    }

    # 一对多
    public function sentence()
    {
        return $this->belongsTo(LearningSentences::class, 'sentence_id', 'sentence_id');
    }
    
    
}
