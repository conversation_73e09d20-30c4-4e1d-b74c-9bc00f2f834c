<?php

namespace app\model;

use support\Model;

/**
 * topics 学习话题管理
 * @property integer $topic_id (主键)
 * @property string $topic_type 预设/自定义
 * @property string $category 预设分类
 * @property string $title_zh 中文标题
 * @property string $title_vi 越南语标题
 * @property integer $user_id 自定义话题创建者
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class LearningTopics extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'learning_topics';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'topic_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    #表关联
    public function sentences()
    {
        return $this->hasMany(LearningSentences::class, 'topic_id', 'topic_id');
    }
    
    
}
