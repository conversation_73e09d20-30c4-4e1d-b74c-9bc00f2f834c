<?php

namespace app\service;

use app\model\LearningSentences;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use plugin\storage\api\Storage;
use support\Log;
use Workerman\Connection\AsyncTcpConnection;
use Webman\Event\Event;

class XunFei
{
    private string $appId;
    private string $apiKey;
    private string $apiSecret;
    private Client $httpClient;
    public function __construct()
    {
        $this->appId = config('ait.xunfei.appid');
        $this->apiKey = config('ait.xunfei.key');
        $this->apiSecret = config('ait.xunfei.secret');
        $this->httpClient = new Client();
    }

    /**
     * 生成讯飞翻译鉴权请求头
     * @param string $body
     * @param string $path
     * @param string $host
     * @return array
     */
    private function buildAuthHeader(string $body, string $path, string $host): array
    {
        $date = gmdate('D, d M Y H:i:s') . ' GMT';
        $digest = "SHA-256=" . base64_encode(hash("sha256", $body, true));
        $signString = sprintf("host: %s\ndate: %s\nPOST %s HTTP/1.1\ndigest: %s",
            $host, $date, $path, $digest);
        $signature = base64_encode(hash_hmac("sha256", $signString, $this->apiSecret, true));

        return [
            'Authorization' => sprintf(
                'api_key="%s", algorithm="hmac-sha256", headers="host date request-line digest", signature="%s"',
                $this->apiKey,
                $signature
            ),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json,version=1.0',
            'Host' => $host,
            'Date' => $date,
            'Digest' => $digest
        ];
    }

    /**
     * 生成讯飞鉴权 URL
     * @param string $baseWsUrl
     * @param string $path
     * @return string
     */
    private function generateWsUrl(string $baseWsUrl, string $path): string
    {
        $host = "ws-api.xfyun.cn";
        $date = gmdate('D, d M Y H:i:s') . ' GMT';
        $signatureOrigin = "host: $host\ndate: $date\nGET $path HTTP/1.1";
        $signatureSha = base64_encode(hash_hmac('sha256', $signatureOrigin, $this->apiSecret, true));
        $authorization = base64_encode(sprintf(
            'api_key="%s", algorithm="hmac-sha256", headers="host date request-line", signature="%s"',
            $this->apiKey,
            $signatureSha
        ));

        return sprintf("%s?authorization=%s&date=%s&host=%s",
            $baseWsUrl,
            urlencode($authorization),
            urlencode($date),
            $host
        );
    }

    /**
     * 讯飞翻译 中文->越南语
     * @param string $text
     * @param string $from
     * @param string $to
     * @return string
     */
    public function translate(string $text, string $from = 'cn', string $to = 'vi'): string
    {
        $requestBody = json_encode([
            'common' => ['app_id' => $this->appId],
            'business' => ['from' => $from, 'to' => $to],
            'data' => ['text' => base64_encode($text)]
        ]);

        try {
            $response = $this->httpClient->post("https://itrans.xfyun.cn/v2/its", [
                'headers' => $this->buildAuthHeader($requestBody, '/v2/its', 'itrans.xfyun.cn'),
                'body' => $requestBody
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return $result['data']['result']['trans_result']['dst'] ?? '';
        } catch (GuzzleException $e) {
            Log::error("[XunFei Translate] Error: " . $e->getMessage());
            return "";
        }
    }

    /**
     * 讯飞语音合成
     */
    public function tts(string $text, int $sentenceId, string $gender = 'female', string $speed = 'standard'): void
    {
        echo "音频合成中...\n";
        Log::info("音频合成中...");
        $audioData = '';
        $ws = new AsyncTcpConnection($this->generateWsUrl("ws://tts-api.xfyun.cn/v2/tts", "/v2/tts"));

        $ws->onMessage = function ($connection, $data) use (&$audioData, $sentenceId, $gender, $speed) {
            $response = json_decode($data, true);
            Log::info("[XunFei TTS] Response", [
                'response' => $response,
            ]);

            if ($response['code'] !== 0) {
                Log::error("[XunFei TTS] API Error: " . $response['message']);
                $connection->close();
                return;
            }

            $audioData .= base64_decode($response['data']['audio']);

            if ($response['data']['status'] == 2) {
                $res = Storage::disk(Storage::MODE_OSS, false)
                    ->uploadBase64("data:audio/mp3;base64," . base64_encode($audioData), 'mp3');
                Log::info("[XunFei TTS] Audio Upload Result", [
                    'res' => $res,
                ]);
                if (!$res) {
                    Log::error("[XunFei TTS] Storage Error: Failed to upload audio",[
                        'res'=>$res,

                    ]);
                    return;
                }

                $sentence = LearningSentences::find($sentenceId);
                $sentence?->audios()->create([
                    'teacher_gender' => $gender,
                    'speed' => $speed,
                    'audio_url' => $res['save_path']
                ]);

                $connection->close();
            }
        };

        $ws->onError = function ($connection, $error) {
            echo "音频合成连接失败: $error\n";
            Log::error("[XunFei TTS] Connection Error: $error");
        };
        $ws->onClose = function ($connection) use ($audioData) {
            unset($audioData);
        };

        $ws->onConnect = function ($connection) use ($text, $gender, $speed) {
            $vcnMap = [
                'female' => 'x4_lingxiaolu_em_v2',
                'male' => 'x4_lingfeizhe_emo'
            ];
            $speedMap = [
                'slow' => 30,
                'standard' => 40,
                'fast' => 50
            ];

            $payload = json_encode([
                "common" => ["app_id" => $this->appId],
                "business" => [
                    "aue" => "lame",
                    "auf" => "audio/L16;rate=16000",
                    "vcn" => $vcnMap[$gender] ?? 'x4_lingxiaoyu_emo',
                    "speed" => $speedMap[$speed] ?? 50,
                    "volume" => 70,
                    "tte" => "utf8"
                ],
                "data" => [
                    "status" => 2,
                    "text" => base64_encode($text),
                    "encoding" => "utf8"
                ]
            ]);

            $connection->send($payload);
        };

        $ws->connect();
    }
    /**
     * 讯飞语音合成优化
     * @param array $userData
     * @param string $gender
     * @param string $speed
     */
    public function tts1(array $userData, string $gender = 'female', string $speed = 'standard'): void
    {
        $audioData = '';
        $ws = new AsyncTcpConnection($this->generateWsUrl("ws://tts-api.xfyun.cn/v2/tts", "/v2/tts"));

        $ws->onMessage = function ($connection, $data) use (&$audioData, $gender, $speed,$userData) {
            $response = json_decode($data, true);

            if ($response['code'] !== 0) {
                Log::error("[XunFei TTS] code:".$response['code']." message".$response['message']);
                echo "[XunFei TTS] code:".$response['code']." message".$response['message'];
                $connection->close();
                return;
            }

            $audioData .= base64_decode($response['data']['audio']);

            if ($response['data']['status'] == 2) {
                $res = Storage::disk(Storage::MODE_OSS, false)
                    ->uploadBase64("data:audio/mp3;base64," . base64_encode($audioData), 'mp3');
                // var_dump($res);
                if (!$res) {
                    Log::error("[XunFei TTS] Storage Error: Failed to upload audio",[
                        'res'=>$res,

                    ]);
                    return;
                }
                # 触发音频创建
                Event::emit('audio.create', [
                    'text' => $userData['text'],
                    'teacher_gender' => $gender,
                    'speed' => $speed,
                    'audio_url' => $res['save_path'],
                ]);

                
                # 判断是什么场景
                // if($userData['mode'] == "场景跟读"){
                //     $dialogue = UserSceneDialogueMessages::find($userData['id']);
                //     $dialogue?->audios()->create([
                //         'teacher_gender' => $gender,
                //         'speed' => $speed,
                //         'audio_url' => $res['save_path']
                //     ]);
                // }
                // if ($userData['mode'] == "示范朗读"){
                //     $sentence = LearningSentences::find($userData['id']);
                //     $sentence?->audios()->create([
                //         'teacher_gender' => $gender,
                //         'speed' => $speed,
                //         'audio_url' => $res['save_path']
                //     ]);
                // }


                $connection->close();
            }
        };

        $ws->onError = function ($connection, $error) {
            echo "音频合成连接失败: $error\n";
            Log::error("[XunFei TTS] Connection Error: $error");
        };
        $ws->onClose = function ($connection) use ($audioData) {
            unset($audioData);
        };

        $ws->onConnect = function ($connection) use ($userData, $gender, $speed) {
            $vcnMap = [
                'female' => 'x4_lingxiaolu_em_v2',
                'male' => 'x4_lingfeizhe_emo'
            ];
            $speedMap = [
                'slow' => 30,
                'standard' => 40,
                'fast' => 50
            ];

            $payload = json_encode([
                "common" => ["app_id" => $this->appId],
                "business" => [
                    "aue" => "lame",
                    "auf" => "audio/L16;rate=16000",
                    "vcn" => $vcnMap[$gender] ?? 'x4_lingxiaoyu_emo',
                    "speed" => $speedMap[$speed] ?? 50,
                    "volume" => 70,
                    "tte" => "utf8"
                ],
                "data" => [
                    "status" => 2,
                    "text" => base64_encode($userData['text']),
                    "encoding" => "utf8"
                ]
            ]);

            $connection->send($payload);
        };

        $ws->connect();
    }

}