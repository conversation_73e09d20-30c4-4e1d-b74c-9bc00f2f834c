<?php

namespace app\service;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use support\Cache;
use support\Log;
use RuntimeException;

class Baidu
{
    // 缓存前缀，用于存储百度接口的Access Token
    private const CACHE_KEY_PREFIX = 'baidu_access_token_';

    // 百度API接口URL
    private const TOKEN_URL = 'https://aip.baidubce.com/oauth/2.0/token';
    private const ASR_URL = 'https://vop.baidu.com/server_api';
    private const TTS_URL = 'https://tsn.baidu.com/text2audio';

    // 发音语速映射
    private const SPEED_MAP = [
        'slow' => 2,
        'standard' => 3,
        'fast' => 5,
    ];

    // 发音人（音色）映射
    private const VCN_MAP = [
        'female' => 4254,
        'male' => 4149,
    ];

    private string $apiKey;
    private string $apiSecret;
    private Client $client;

    /**
     * 构造函数，初始化百度API参数
     */
    public function __construct()
    {
        $this->apiKey = config('ait.baidu.key');
        $this->apiSecret = config('ait.baidu.secret');
        $this->client = $client ?? new Client();
    }

    /**
     * 获取缓存key，防止不同API Key冲突
     */
    private function cacheKey(): string
    {
        return self::CACHE_KEY_PREFIX . md5($this->apiKey);
    }

    /**
     * 获取百度API Access Token，缓存避免频繁请求
     */
    private function getAccessToken(): string
    {
        if ($token = Cache::get($this->cacheKey())) {
            return $token;
        }

        try {
            $response = $this->client->post(self::TOKEN_URL, [
                'form_params' => [
                    'grant_type'    => 'client_credentials',
                    'client_id'     => $this->apiKey,
                    'client_secret' => $this->apiSecret
                ]
            ]);

            $body = json_decode($response->getBody(), true);

            if (empty($body['access_token']) || empty($body['expires_in'])) {
                Log::error("[Baidu] 令牌响应无效", ['response' => $body]);
                throw new RuntimeException('令牌响应无效');
            }

            Cache::set($this->cacheKey(), $body['access_token'], $body['expires_in'] - 300);

            return $body['access_token'];

        } catch (GuzzleException $e) {
            Log::error("[Baidu] 检索令牌无效", ['exception' => $e->getMessage()]);
            throw new RuntimeException('检索令牌无效', 0, $e);
        }
    }

    /**
     * 百度语音识别（ASR）接口，接受Base64音频数据，返回识别文字
     */
    public function asrFromBase64(string $audioBase64, string $format = 'pcm', int $rate = 16000, int $channel = 1): array
    {
        $rawData = base64_decode($audioBase64, true);
        if ($rawData === false) {
            throw new RuntimeException("提供的 base64 音频数据无效");
        }

        $payload = [
            'format'  => $format,
            'rate'    => $rate,
            'channel' => $channel,
            'cuid'    => uniqid('asr_', true),
            'speech'  => $audioBase64,
            'len'     => strlen($rawData),
            'token'   => $this->getAccessToken(),
        ];

        try {
            $response = $this->client->post(self::ASR_URL, ['json' => $payload]);
            $result = json_decode($response->getBody(), true);

            if (($result['err_no'] ?? -1) !== 0) {
                Log::error("[Baidu ASR] 错误响应", ['response' => $result]);
                throw new RuntimeException("ASR Error [{$result['err_no']}]: {$result['err_msg']}");
            }

            return $result['result'] ?? [];

        } catch (GuzzleException $e) {
            Log::error("[Baidu ASR] 请求失败", ['exception' => $e->getMessage()]);
            throw new RuntimeException('ASR 请求失败', 0, $e);
        }
    }

    /**
     * 百度文本转语音（TTS）接口，返回Base64编码音频数据
     */
    public function tts(string $text, string $teacherGender = "female", string $speed = "standard"): string
    {
        $params = [
            'tex'  => $text,
            'tok'  => $this->getAccessToken(),
            'cuid' => uniqid('tts_', true),
            'ctp'  => 1,
            'lan'  => 'zh',
            'spd'  => self::SPEED_MAP[$speed] ?? self::SPEED_MAP['standard'],
            'pit'  => 5,
            'vol'  => 5,
            'per'  => self::VCN_MAP[$teacherGender] ?? self::VCN_MAP['female'],
            'aue'  => 3,
        ];

        try {
            $response = $this->client->post(self::TTS_URL, ['form_params' => $params, 'http_errors' => false]);

            if (str_contains($response->getHeaderLine('Content-Type'), 'audio/')) {
                return base64_encode($response->getBody());
            }

            Log::error("[Baidu TTS] 错误响应", ['response' => (string)$response->getBody()]);
            throw new RuntimeException('TTS error: ' . (string)$response->getBody());

        } catch (GuzzleException $e) {
            Log::error("[Baidu TTS] 请求失败", ['exception' => $e->getMessage()]);
            throw new RuntimeException('TTS 请求失败', 0, $e);
        }
    }
}