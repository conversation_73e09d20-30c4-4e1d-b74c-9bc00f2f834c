<?php

namespace app\service;

use Webman\Openai\Chat;
use InvalidArgumentException;

/**
 * AI服务类，用于处理与OpenAI API的交互
 */
class Ai
{
    private readonly string $apiKey;
    private readonly string $apiEndpoint;
    private readonly string $model;

    /**
     * 使用配置初始化AI服务
     * 
     * @throws InvalidArgumentException 如果缺少必需的配置
     */
    public function __construct()
    {
        $this->apiKey = config('ait.ai.API_KEY');
        $this->apiEndpoint = config('ait.ai.API');
        $this->model = config('ait.ai.MODEL');

        if (empty($this->apiKey) || empty($this->apiEndpoint) || empty($this->model)) {
            throw new InvalidArgumentException('缺少必需的AI配置');
        }
    }

    /**
     * 向AI服务发起补全请求
     *
     * @param array $data 请求数据，包括消息和其他参数
     * @param callable $completeCallback 用于处理完成响应的回调函数
     * @param callable|null $streamCallback 可选，用于处理流式响应的回调函数
     * @throws \Exception 如果API请求失败
     */
    public function completions(array $data, callable $completeCallback, ?callable $streamCallback = null): void
    {
        try {
            $chat = new Chat([
                'apikey' => $this->apiKey,
                'api' => $this->apiEndpoint
            ]);

            // 确保使用配置中的模型
            $data['model'] = $this->model;
            // 温度
            $data['temperature'] = 1;
            // // 频率惩罚
            // $data['frequency_penalty'] = 0.7;
            // // 存在惩罚
            // $data['presence_penalty'] = 0.5;

            $callbacks = [
                'complete' => $completeCallback,
            ];

            if ($streamCallback) {
                $callbacks['stream'] = $streamCallback;
            }

            $chat->completions($data, $callbacks);
        } catch (\Exception $e) {
            // 记录错误或进行适当处理
            throw new \Exception('AI补全请求失败: ' . $e->getMessage(), 0, $e);
        }
    }
}