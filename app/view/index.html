<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话系统 - 测试工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="gradient-bg text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-5xl font-bold mb-4">AI对话系统</h1>
            <p class="text-xl opacity-90">专业的对话生成与质量评估工具</p>
        </div>
    </div>

    <div class="container mx-auto px-4 py-16">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <!-- 对话质量检查 -->
            <div class="bg-white rounded-lg shadow-lg p-8 card-hover">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">💬</span>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">对话质量检查</h2>
                    <p class="text-gray-600">随机展示10组对话，帮助评估对话质量</p>
                </div>

                <div class="space-y-4 mb-6">
                    <div class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        随机获取对话样本
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        支持话题和难度筛选
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        质量评估与统计
                    </div>
                </div>

                <a href="#" onclick="navigateTo('quality')" class="block w-full bg-blue-600 text-white text-center py-3 rounded-md hover:bg-blue-700 transition-colors font-medium">
                    开始质量检查
                </a>
            </div>

            <!-- 内容生成测试 -->
            <div class="bg-white rounded-lg shadow-lg p-8 card-hover">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-3xl">🤖</span>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">内容生成测试</h2>
                    <p class="text-gray-600">测试AI模型生成对话的质量和效果</p>
                </div>

                <div class="space-y-4 mb-6">
                    <div class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                        支持多种AI模型切换
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                        场景跟读 & 自由对话
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                        实时生成与质量评估
                    </div>
                </div>

                <a href="#" onclick="navigateTo('generator')" class="block w-full bg-purple-600 text-white text-center py-3 rounded-md hover:bg-purple-700 transition-colors font-medium">
                    开始生成测试
                </a>
            </div>
        </div>

        <!-- 功能特色 -->
        <div class="mt-16 text-center">
            <h3 class="text-2xl font-bold text-gray-800 mb-8">系统特色</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">⚡</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">高效测试</h4>
                    <p class="text-sm text-gray-600">快速生成和评估对话内容</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🎯</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">精准评估</h4>
                    <p class="text-sm text-gray-600">多维度质量评估体系</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🔧</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">灵活配置</h4>
                    <p class="text-sm text-gray-600">支持多种模型和参数调整</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p class="text-gray-400">AI对话系统测试工具 - 专注于对话质量提升</p>
        </div>
    </footer>

    <script>
        // 检测当前路径是否包含 /ai 前缀
        function getBasePath() {
            const path = window.location.pathname;
            return path.startsWith('/ai') ? '/ai' : '';
        }

        // 导航到指定页面
        function navigateTo(page) {
            const basePath = getBasePath();
            window.location.href = basePath + '/' + page;
        }
    </script>
</body>
</html>
