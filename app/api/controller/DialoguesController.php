<?php

namespace app\api\controller;

use app\model\LearningTopics;
use app\model\User;
use app\model\UserSceneDialogueMessages;
use app\model\UserSceneDialogueSessions;
use app\service\Baidu;
use support\Redis;
use support\Request;
use support\Response;
use Webman\RateLimiter\Limiter;
use Webman\RedisQueue\Client;
use Webman\RedisQueue\Redis as QueRedis;
use app\service\XunFei;
use app\model\PresetSceneDialogues;
use app\model\PresetSceneTemplates;
use app\model\AudioResources;
use Webman\Event\Event;
# 场景对话
class DialoguesController
{
    protected $noNeedLogin = [
        'index',
        'cs',
        'cs1'
    ];
    public function index(Request $request)
    {
        $data = ['topic_id' => LearningTopics::first()->topic_id, 'difficulty' => 'hard'];
        // 队列名
        $queue = 'create_dialogues';
        // 投递消息
        // Client::send($queue, $data);
        QueRedis::send($queue, $data);
    }

    public function cs(Request $request): Response
    {
        $text = $request->input('text');
        $baidu = new Baidu();
        $res = $baidu->tts($text);
        return toJson(200, 'success', $res);
    }
    public function cs1(Request $request): Response
    {
        $file = $request->file('file');
        if (!$file) {
            return toJson(400, '请上传音频文件');
        }
        # 判断文件类型是pcm还是mp4
        // 获取文件的 MIME 类型和扩展名
        //        $mimeType = $file->getUploadMimeType();
        //        $extension = strtolower($file->getUploadExtension()); // e.g., pcm, mp4, wav
        //
        //        // 判断是否为 PCM 或 MP4
        //        $allowedTypes = ['pcm', 'mp3'];
        //        if (!in_array($extension, $allowedTypes)) {
        //            return json([
        //                'code' => 400,
        //                'msg' => '仅支持 PCM 或 MP4 格式的文件',
        //                'detected_type' => $extension,
        //                'mime_type' => $mimeType
        //            ]);
        //        }


        # 读取文件流并转为base64
        $audioData = base64_encode(file_get_contents($file->getRealPath()));
        $baidu = new Baidu();
        $res = $baidu->asrFromBase64($audioData);
        return toJson(200, 'success', $res);
    }
    public function createDialogues(Request $request, XunFei $xunfei): Response
    {
        $topicId = $request->input('topic_id', 0);
        # 场景类型
        $module = $request->input('module', 3);

        $topicZh = $request->input('topic_zh');
        $topicVi = $request->input('topic_vi');
        # 根据传入参数翻译
        if ($topicZh) {
            $topicVi = $xunfei->translate($topicZh);
        } else if ($topicVi) {
            $topicZh = $xunfei->translate($topicVi, 'vi', 'cn');
        }



        $difficulty = $request->input('difficulty');
        $user_id = $request->user->user_id;

        // Validate if topic_id exists
        $topic = LearningTopics::find($topicId);
        if (!$topic && !$topicZh && !$topicVi) {
            return toJson(400, '缺少参数');
        }

        # 限流 key
        $key = "create_session-$module-$user_id";
        try {
            Limiter::check($key, 1, 10, '请求过于频繁，请稍后再试');
        } catch (\Exception $e) {
            return toJson(429, '请求过于频繁，请稍后再试');
        }


        $dialogueSession = UserSceneDialogueSessions::create([
            'user_id'        => $user_id,
            'learning_mode'  => $module == 3 ? '场景跟读' : '自由对话',
            'difficulty'     => $difficulty,
            'status'         => 'ongoing',
            'topic_id'       => $topic?->topic_id,
            'custom_topic_zh' => $topic ? $topic->title_zh : $topicZh,
            'custom_topic_vi' => $topic ? $topic->title_vi : $topicVi,
        ]);
        if ($module == 3) {




          
            # 从预设库中随机取一个
            $template = PresetSceneTemplates::where('topic_id', $topicId)->where('difficulty', $difficulty)->inRandomOrder()->first();
            if ($template) {
                echo "随机";
                $dialogue = PresetSceneDialogues::where('template_id', $template->template_id)->orderBy('sort_order')->get();
                # 写入用户对话记录
                // foreach ($dialogue as $index => $item) {
                //     UserSceneDialogueMessages::create([
                //         'session_id'  => $dialogueSession->session_id,
                //         'role'        => $item->role,
                //         'sender'      => $item->role == 'system' ? '系统' : '用户',
                //         'sort_order'  => $item->sort_order,
                //         'content_zh'  => $item->content_zh,
                //         'content_vi'  => $item->content_vi,
                //     ]);
                // }

                // 写入用户对话记录（批量插入优化）
                $sessionId = $dialogueSession->session_id;
                $data = [];

                foreach ($dialogue as $item) {
                    $data[] = [
                        'session_id'  => $sessionId,
                        'role'        => $item->role,
                        'sender'      => $item->role == 'system' ? '系统' : '用户',
                        'sort_order'  => $item->sort_order,
                        'content_zh'  => $item->content_zh,
                        'content_vi'  => $item->content_vi,
                    ];
                }

                // 批量插入（每次最多插入1000条）
                foreach (array_chunk($data, 1000) as $chunk) {
                    UserSceneDialogueMessages::insert($chunk);
                }
                # 更新状态
                $dialogueSession->status = 'completed';
                $dialogueSession->save();
            } else {
                # 场景跟读
                $data = [
                    'topic_id'         => $dialogueSession->topic_id,
                    'custom_topic_zh' => $dialogueSession->custom_topic_zh,
                    'difficulty'       => $difficulty,
                    'dialogue_session_id' => $dialogueSession->session_id
                ];
                // Client::send('create_dialogues', $data);
                QueRedis::send('create_dialogues', $data);
            }
        } else {
            echo "触发自由对话";
            # 自由对话
            $data = [
                'topic_id'         => $dialogueSession->topic_id,
                'custom_topic_zh' => $dialogueSession->custom_topic_zh,
                'difficulty'       => $difficulty,
                'dialogue_session_id' => $dialogueSession->session_id
            ];
            # TODO: 修改为通过dialogueSession
            var_dump($data);
            // Client::send('create_dialogues_free', $data);
            QueRedis::send('create_dialogues_free', $data);
        }

        return toJson(200, 'success', $dialogueSession);
    }
    /**
     * 根据 Session ID 获取对话内容
     */
    public function getDialogues(Request $request): Response
    {
        $session_id = $request->input('session_id');
        $dialogueSession = UserSceneDialogueSessions::find($session_id);
        $data = [
            'session_id' => $dialogueSession->session_id,
            'topic_id' => $dialogueSession->topic_id,
            'difficulty' => $dialogueSession->difficulty,
            'status' => $dialogueSession->status,
            'messages' => $dialogueSession->messages()->orderBy('sort_order')->get(),
        ];
        if (!$dialogueSession) {
            return toJson(400, '对话不存在');
        }
        $dialogueSession->messages;
        return toJson(200, 'success', $data);
    }
    /**
     * 根据 Session ID 发送对话消息
     */
    public function sendDialogues(Request $request, XunFei $xunFei): Response
    {
        $session_id = $request->input('session_id');
        $message = $request->input('message');
        $dialogueSession = UserSceneDialogueSessions::find($session_id);
        # 判断对话是否存在
        if (!$dialogueSession) {
            return toJson(400, '对话不存在');
        }
        # 判断消息是否为空
        if (!$message) {
            return toJson(400, '消息不能为空');
        }
        # 判断对话是否完成
        if ($dialogueSession->status == 'completed') {
            return toJson(400, '对话已结束');
        }
        # 判断对话是否准备就绪
        if ($dialogueSession->status != 'ready') {
            return toJson(400, '对话未准备就绪');
        }
        $messages = $dialogueSession->messages;
        # 判断消息是否为空
        if ($messages->isEmpty()) {
            return toJson(400, '对话为空');
        }
        # 最后一句如果是用户消息，则返回
        if ($messages->last()->role == 'user') {
            return toJson(400, '系统正在生成对话，请稍后再试', $dialogueSession);
        } else {
            # 关联创建消息
            $dialogueSession->messages()->create([
                'role' => 'user',
                'sender' => '用户',
                'content_zh' => $message,
                'content_vi' => $xunFei->translate($message),
                'sort_order' => $messages->last()->sort_order + 1,
            ]);
            # 更新状态
            $dialogueSession->status = 'ongoing';
            $dialogueSession->save();
            # 刷新
            $dialogueSession->refresh();
            $data = [
                'topic_id'         => $dialogueSession->topic_id,
                'custom_topic_zh' => $dialogueSession->custom_topic_zh,
                'difficulty'       => $dialogueSession->difficulty,
                'dialogue_session_id' => $dialogueSession->session_id
            ];
            echo "free_对话\n";
            // Client::send('create_dialogues_free', $data);
            QueRedis::send('create_dialogues_free', $data);
        }


        return toJson(200, 'success', $dialogueSession);
    }

    // 获取句子音频
    public function audio(Request $request)
    {
        if (!$message_id = $request->input('message_id')) {
            return toJson(400, '参数错误');
        }

        if (!$message = UserSceneDialogueMessages::find($message_id)) {
            return toJson(400, '句子不存在');
        }

        $userPreferences = optional(User::find($request->user->user_id))->userPreferences;
        $teacher_gender = $userPreferences->teacher_gender ?? 'female';
        $speed = $userPreferences->speech_speed ?? 'standard';
        # 获取音频
        $audio = AudioResources::getAudio($message->content_zh, $teacher_gender, $speed);

        if (is_null($audio)) {#!$audio = $message->audios()->where(compact('teacher_gender', 'speed'))->first()
            $data = [
                'text' => $message->content_zh,
                'mode' => '场景跟读',
                'id' => $message_id,
                'teacher_gender' => $teacher_gender,
                'speed' => $speed,
            ];
            $key = 'audio_gen_task:dialogues_' . $message_id . ':' . md5(json_encode($data));
            if (Redis::get($key)) {
                return toJson(202, '音频正在生成中，请稍后再试');
            }
            # 触发音频生成
            Event::emit('audio.gen', $data);
            Redis::setex($key, 60, 1);
            
            return toJson(400, '音频不存在');
        }
        return toJson(200, 'success', $audio);
    }
}
