<?php

namespace app\controller;

use support\Request;
use Workerman\Connection\AsyncTcpConnection;

class SpeechController
{
    private $noNeedLogin = [
        'speechToText'
    ];
    private $APP_ID;
    private $API_KEY;
    private $API_SECRET;
    private $WS_URL = "ws://cn-east-1.ws-api.xf-yun.com/v1/private/s8e098720";
    # 初始化
    public function __construct()
    {
        $this->APP_ID = config('ait.xunfei.appid');
        $this->API_KEY = config('ait.xunfei.key');
        $this->API_SECRET = config('ait.xunfei.secret');
    }

    /**
     * 语音转文本 API 接口
     */
    public function speechToText(Request $request)
    {
        if (!$request->file('file')) {
            return toJson(400, '缺少参数 file');
        }

        // 获取音频数据
        $file = $request->file('file');
        $audioData = file_get_contents($file->getRealPath());

        // 处理音频
        $result = $this->processAudio($audioData);
        return toJson(200, 'success', $result);
    }

    /**
     * 处理音频数据并调用 WebSocket API
     */
    private function processAudio($audioData)
    {
        $result = [];

        // 创建 WebSocket 连接
        $authUrl = $this->createAuthUrl($this->WS_URL);
        $ws = new AsyncTcpConnection($authUrl);

        $ws->onMessage = function ($connection, $data) use (&$result) {
            $response = json_decode($data, true);
            if (isset($response['header']['status']) && $response['header']['status'] == 2) {
                $result['text'] = base64_decode($response['payload']['result']['text']);
                echo $result['text'];
                $connection->close();
            }
        };

        $ws->onError = function ($connection, $error) use (&$result) {
            $result['error'] = "WebSocket Error: " . $error;
        };

        $ws->onClose = function ($connection) {
        };

        $ws->onConnect = function ($connection) use ($audioData) {
            $requestPayload = [
                "header" => ["app_id" => $this->APP_ID, "status" => 2],
                "parameter" => [
                    "st" => [
                        "lang" => "cn",
                        "core" => "para",
                        "refText" => "您好，欢迎来到科大讯飞。",
                        "result" => ["encoding" => "utf8", "compress" => "raw", "format" => "plain"],
                        "paragraph_need_word_score" => 1,
                    ]
                ],
                "payload" => [
                    "data" => [
                        "encoding" => "lame",
                        "sample_rate" => 16000,
                        "channels" => 1,
                        "bit_depth" => 16,
                        "status" => 2,
                        "seq" => 0,
                        "audio" => base64_encode($audioData)
                    ]
                ]
            ];
            $connection->send(json_encode($requestPayload));
        };

        // 启动连接
        $ws->connect();

        return $result;
    }

    /**
     * 生成 API 认证 URL
     */
    private function createAuthUrl($url, $method = "GET")
    {
        $parsedUrl = parse_url($url);
        $host = $parsedUrl['host'];
        $path = $parsedUrl['path'];
        $date = gmdate('D, d M Y H:i:s T');

        $signatureString = "host: $host\ndate: $date\n$method $path HTTP/1.1";
        $signature = base64_encode(hash_hmac('sha256', $signatureString, $this->API_SECRET, true));

        $authorization = base64_encode("api_key=\"{$this->API_KEY}\", algorithm=\"hmac-sha256\", headers=\"host date request-line\", signature=\"$signature\"");

        return "$url?" . http_build_query([
                "host" => $host,
                "date" => $date,
                "authorization" => $authorization
            ]);
    }
}
