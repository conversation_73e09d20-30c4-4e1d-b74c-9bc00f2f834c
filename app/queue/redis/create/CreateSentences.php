<?php

namespace app\queue\redis\create;

use app\model\AiPromptTemplates;
use app\model\LearningSentences;
use support\Log;
use Webman\RedisQueue\Client;
use Webman\RedisQueue\Consumer;
use app\service\Ai;
use Webman\RedisQueue\Redis as QueRedis;
class CreateSentences implements Consumer
{
    // 示范句子生成队列
    public $queue = 'create-sentences';

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接`
    public $connection = 'default';
    /**
     * 场景提示词映射
     */
    protected const PROMPT_MAP = [
        '导游中文' => 'demo_reading_dyzw',
        '工厂中文' => 'demo_reading_gczw',
        '商务中文' => 'demo_reading_swzw',
        '电商中文' => 'demo_reading_dszw',
        '中国求学' => 'demo_reading_zgqx',
        '时下热点' => 'demo_reading_sxrd',
    ];



    // 消费
    public function consume($data)
    {
        // 生成主题
        $category = $data['category'];
        $category_prompt = self::PROMPT_MAP[$category]?? 'demo_reading';

        $topic_id = $data['topic_id'];
        $aiPrompts = AiPromptTemplates::where('module', $category_prompt)->first();
        $params = [
            "count" => $data['count']
        ];
        echo "category: $category, topic_id: $topic_id, count: {$data['count']} , category_prompt: $category_prompt\n";
        if (!$aiPrompts) {
            Log::error('AiPrompts not found');
            echo 'AiPrompts not found';
            return;
        }
        // 使用 strtr 替换参数，简化代码
        $aiPrompts->prompt_template = strtr($aiPrompts->prompt_template, array_combine(
            array_map(fn($key) => "{{{$key}}}", array_keys($params)),
            array_values($params)
        ));
        $data = [
            'model' => getenv('AI_MODEL'),
            'temperature' => 0.8,
            'messages' => [
                ['role' => 'system', 'content' => $aiPrompts->prompt_template],
                ['role' => 'user', 'content' => $category]
            ],
        ];
        $ai = new Ai();
        $ai->completions($data, function ($response)use($topic_id) {
            $choices = $response['choices'][0]['message']['content'];
            // var_dump($choices);
            $sz = json_decode($choices, true);
            # 写入队列 update-sentences
            $queue = 'update-sentences';
            foreach ($sz as $item) {
                // 投递消息
                // Client::send($queue, [
                //     'topic_id' => $topic_id,
                //     'content_zh' => $item,
                // ]);
                QueRedis::send($queue, [
                    'topic_id' => $topic_id,
                    'content_zh' => $item,
                ]);
            }
        });

    }
            
    public function onConsumeFailure(\Throwable $e, $package)
    {
        echo "consume failure\n";
        echo $e->getMessage() . "\n";
        // 无需反序列化
        var_export($package); 
        # 写入日志
        Log::error($e->getMessage(),$package);
    }
}
