<?php

namespace app\queue\redis\audio;

use app\service\XunFei;
use support\Log;
use Webman\RedisQueue\Consumer;

class AudioGenTask implements Consumer
{
    // 要消费的队列名
    public $queue = 'audio_gen_task';

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接`
    public $connection = 'default';

    // 讯飞音频生成
    public function consume($data)
    {
        echo "consume audio_gen_task\n";
        $teacher_gender = $data['teacher_gender'];
        $speed = $data['speed'];
        $xunfei = new XunFei();
        $xunfei->tts1($data, $teacher_gender, $speed);
    }

    public function onConsumeFailure(\Throwable $e, $package)
    {
        echo "consume failure\n";
        echo $e->getMessage() . "\n";
        // 无需反序列化
        var_export($package); 
        # 写入日志
        Log::error($e->getMessage(),$package);
    }
            
}
