<?php

namespace app\queue\redis\update;

use app\model\LearningSentences;
use app\service\XunFei;
use Webman\RedisQueue\Consumer;
use support\Log;

class UpdateSentences implements Consumer
{
    // 要消费的队列名
    public $queue = 'update-sentences';

    // 连接名，对应 plugin/webman/redis-queue/redis.php 里的连接`
    public $connection = 'default';

    // 消费
    public function consume($data)
    {
        $topic_id = $data['topic_id'];
        $content_zh = $data['content_zh'];
        $xunfei = new XunFei();
        $content_vi = $xunfei->translate($content_zh);
        # 如果中文已存在于数据库中
        if (LearningSentences::where('topic_id', $topic_id)->where('content_zh', $content_zh)->exists()) {
            echo "中文已存在于数据库中\n";
            return;
        }
        // 保存到数据库
        $sentence = new LearningSentences();
        $sentence->topic_id = $topic_id;
        $sentence->content_zh = $content_zh;
        $sentence->content_vi = $content_vi;
        $sentence->save();
    }


    public function onConsumeFailure(\Throwable $e, $package)
    {
        echo "consume failure\n";
        echo $e->getMessage() . "\n";
        // 无需反序列化
        var_export($package); 
        # 写入日志
        Log::error($e->getMessage(),$package);
        
    }
}
