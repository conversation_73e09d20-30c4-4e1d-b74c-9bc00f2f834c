<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>设置页面</title>
    <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css"/>
    <link rel="stylesheet" href="/app/admin/admin/css/reset.css"/>
</head>
<body class="pear-container">


<div class="layui-card">
    <div class="layui-card-body">

        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this" permission="app.dingtalk.admin.setting.basic">基础配置</li>
                <li permission="app.dingtalk.admin.setting.local">私有云（本地）</li>
                <li permission="app.dingtalk.admin.setting.oss">阿里云</li>
                <li permission="app.dingtalk.admin.setting.cos">腾讯云</li>
                <li permission="app.dingtalk.admin.setting.qiniu">七牛云</li>
                <li permission="app.dingtalk.admin.setting.s3">亚马逊（S3）</li>
                <li permission="app.dingtalk.admin.setting.test">测试上传</li>
            </ul>
            <div class="layui-tab-content">
                <!-- 基础配置 -->
                <div class="layui-tab-item  layui-show">
                    <form class="layui-form" onsubmit="return false" lay-filter="saveBasicSetting" style="width: 720px">
                        <div class="layui-form-item">
                            <label class="layui-form-label">默认存储</label>
                            <div class="layui-input-block">
                                <input type="radio" name="default" value="local" title="私有云" checked="">
                                <input type="radio" name="default" value="oss" title="阿里云">
                                <input type="radio" name="default" value="cos" title="腾讯云">
                                <input type="radio" name="default" value="qiniu" title="七牛云">
                                <input type="radio" name="default" value="s3" title="亚马逊（S3）">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">单个文件限制</label>
                            <div class="layui-input-inline">
                                <input type="text" name="single_limit" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="1024">
                            </div>
                            <div class="layui-form-mid layui-word-aux">单位：字节</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所有文件限制</label>
                            <div class="layui-input-inline">
                                <input type="text" name="total_limit" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="1024">
                            </div>
                            <div class="layui-form-mid layui-word-aux">单位：字节</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">文件数量限制</label>
                            <div class="layui-input-inline">
                                <input type="text" name="nums" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="1">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">允许文件类型</label>
                            <div class="layui-input-inline">
                                <input type="text" name="allow_extension" required lay-verify="required"
                                       autocomplete="off" class="layui-input" placeholder="png,jpg,pdf">
                            </div>
                            <div class="layui-form-mid layui-word-aux">请以英文逗号分隔</div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                                        lay-filter="saveBasicSetting">
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- 本地配置 -->
                <div class="layui-tab-item">
                    <form class="layui-form" onsubmit="return false" lay-filter="saveLocalSetting" style="width: 720px">
                        <div class="layui-form-item">
                            <label class="layui-form-label">存储目录</label>
                            <div class="layui-input-block">
                                <input type="radio" name="root" value="runtime" title="不公开（runtime目录）" checked="">
                                <input type="radio" name="root" value="public" title="公开（public目录）">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">目录名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="dirname" required autocomplete="off"
                                       class="layui-input" placeholder="如：storage">
                            </div>
                            <div class="layui-form-mid layui-word-aux">如：storage（不填写默认为根目录）</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">域名地址</label>
                            <div class="layui-input-inline">
                                <input type="text" name="domain" class="layui-input"
                                       placeholder="如：https://oss.tinywan.com">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                                        lay-filter="saveLocalSetting">
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 阿里云配置 -->
                <div class="layui-tab-item">
                    <form class="layui-form" onsubmit="return false" lay-filter="saveOssSetting" style="width: 520px">
                        <blockquote class="layui-elem-quote layui-bg-gray">
                            需要安装依赖包 composer require aliyuncs/oss-sdk-php
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">accessKeyId</label>
                            <div class="layui-input-block">
                                <input type="text" name="accessKeyId" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">accessKeySecret</label>
                            <div class="layui-input-block">
                                <input type="text" name="accessKeySecret" required lay-verify="required"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">Bucket名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="bucket" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="如：image">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">文件存储目录</label>
                            <div class="layui-input-inline">
                                <input type="text" name="dirname" required autocomplete="off"
                                       class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">不填写默认为根目录</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">域名地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="domain" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="如：https://oss.tinywan.com">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">EndPoint</label>
                            <div class="layui-input-block">
                                <input type="text" name="endpoint" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                                        lay-filter="saveOssSetting">
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 腾讯云配置 -->
                <div class="layui-tab-item">
                    <form class="layui-form" onsubmit="return false" lay-filter="saveCosSetting" style="width: 520px">
                        <blockquote class="layui-elem-quote layui-bg-gray">
                            需要安装依赖包 composer require qcloud/cos-sdk-v5
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">secretId</label>
                            <div class="layui-input-block">
                                <input type="text" name="secretId" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">secretKey</label>
                            <div class="layui-input-block">
                                <input type="text" name="secretKey" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">Bucket名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="bucket" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="如：img">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">文件存储目录</label>
                            <div class="layui-input-inline">
                                <input type="text" name="dirname" required autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">不填写默认为根目录</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">域名地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="domain" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="如：https://oss.tinywan.com">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">Region</label>
                            <div class="layui-input-block">
                                <input type="text" name="region" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                                        lay-filter="saveCosSetting">
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 七牛云配置 -->
                <div class="layui-tab-item">
                    <form class="layui-form" onsubmit="return false" lay-filter="saveQiniuSetting" style="width: 520px">
                        <blockquote class="layui-elem-quote layui-bg-gray">
                            需要安装依赖包 composer require qiniu/php-sdk
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">accessKey</label>
                            <div class="layui-input-block">
                                <input type="text" name="accessKey" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">secretKey</label>
                            <div class="layui-input-block">
                                <input type="text" name="secretKey" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">Bucket名称</label>
                            <div class="layui-input-block">
                                <input type="text" name="bucket" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="如：shanghai">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">文件存储目录</label>
                            <div class="layui-input-inline">
                                <input type="text" name="dirname" required autocomplete="off"
                                       class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">不填写默认为根目录</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">域名地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="domain" required lay-verify="required" autocomplete="off"
                                       class="layui-input" placeholder="如：https://oss.tinywan.com">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                                        lay-filter="saveQiniuSetting">
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 亚马逊（S3）配置 -->
                <div class="layui-tab-item">
                    <form class="layui-form" onsubmit="return false" lay-filter="saveS3Setting" style="width: 520px">
                        <blockquote class="layui-elem-quote layui-bg-gray">
                            需要安装依赖包 composer require league/flysystem-aws-s3-v3
                        </blockquote>
                        <div class="layui-form-item">
                            <label class="layui-form-label">key</label>
                            <div class="layui-input-block">
                                <input type="text" name="key" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">secret</label>
                            <div class="layui-input-block">
                                <input type="text" name="secret" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">bucket</label>
                            <div class="layui-input-block">
                                <input type="text" name="bucket" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">目录名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="dirname" required autocomplete="off"
                                       class="layui-input" placeholder="如：upload">
                            </div>
                            <div class="layui-form-mid layui-word-aux">不填写默认为根目录</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">访问域名</label>
                            <div class="layui-input-block">
                                <input type="text" name="domain" required lay-verify="required" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">region</label>
                            <div class="layui-input-block">
                                <input type="text" name="region" required lay-verify="required" autocomplete="off"
                                       class="layui-input" value="S3_REGION">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">version</label>
                            <div class="layui-input-block">
                                <input type="text" name="version" required lay-verify="required" autocomplete="off"
                                       class="layui-input" value="latest">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">endpoint</label>
                            <div class="layui-input-block">
                                <input type="text" name="endpoint" required lay-verify="required" autocomplete="off"
                                       class="layui-input" value="S3_ENDPOINT">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">acl</label>
                            <div class="layui-input-block">
                                <input type="text" name="acl" required lay-verify="required" autocomplete="off"
                                       class="layui-input" value="public-read">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit=""
                                        lay-filter="saveS3Setting">
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="layui-tab-item">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn" id="test1">上传图片</button>
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" id="demo1">
                            <p id="demoText"></p>
                        </div>
                        <div style="width: 100%; height: 60px">
                            <div class="layui-progress layui-progress-big" lay-showpercent="yes" lay-filter="demo">
                                <div class="layui-progress-bar" lay-percent=""></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>


<script src="/app/admin/component/layui/layui.js"></script>
<script src="/app/admin/component/pear/pear.js"></script>
<script src="/app/admin/admin/js/common.js"></script>

<!-- 测试 -->
<script>
    layui.use(["form", "popup", "upload", "element"], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let upload = layui.upload;
        let element = layui.element;

        $.ajax({
            url: "/app/storage/admin/setting/get",
            success: function (res) {
                if (res.code) {
                    return layui.popup.failure(res.msg);
                }
                layui.each(res.data, function (index, item) {
                    console.log(index); // 得到下标
                    console.log(item); // 得到数据
                    form.val(index, item);
                });
            }
        });

        // 保存基础设置
        form.on("submit(saveBasicSetting)", function (data) {
            $.ajax({
                url: "/app/storage/admin/setting/saveBasic",
                dataType: "json",
                type: "POST",
                data: data.field,
                success: function (res) {
                    if (res.code) {
                        return layui.popup.failure(res.msg);
                    }
                    return layui.popup.success("操作成功");
                }
            });
            return false;
        });

        // 保存本地设置
        form.on("submit(saveLocalSetting)", function (data) {
            $.ajax({
                url: "/app/storage/admin/setting/saveLocal",
                dataType: "json",
                type: "POST",
                data: data.field,
                success: function (res) {
                    if (res.code) {
                        return layui.popup.failure(res.msg);
                    }
                    return layui.popup.success("操作成功");
                }
            });
            return false;
        });

        // 保存阿里云设置
        form.on("submit(saveOssSetting)", function (data) {
            $.ajax({
                url: "/app/storage/admin/setting/saveOss",
                dataType: "json",
                type: "POST",
                data: data.field,
                success: function (res) {
                    if (res.code) {
                        return layui.popup.failure(res.msg);
                    }
                    return layui.popup.success("操作成功");
                }
            });
            return false;
        });

        // 保存腾讯云设置
        form.on("submit(saveCosSetting)", function (data) {
            $.ajax({
                url: "/app/storage/admin/setting/saveCos",
                dataType: "json",
                type: "POST",
                data: data.field,
                success: function (res) {
                    if (res.code) {
                        return layui.popup.failure(res.msg);
                    }
                    return layui.popup.success("操作成功");
                }
            });
            return false;
        });

        // 保存七牛设置
        form.on("submit(saveQiniuSetting)", function (data) {
            $.ajax({
                url: "/app/storage/admin/setting/saveQiniu",
                dataType: "json",
                type: "POST",
                data: data.field,
                success: function (res) {
                    if (res.code) {
                        return layui.popup.failure(res.msg);
                    }
                    return layui.popup.success("操作成功");
                }
            });
            return false;
        });

        // 保存亚马逊（S3）设置
        form.on("submit(saveS3Setting)", function (data) {
            $.ajax({
                url: "/app/storage/admin/setting/saveS3",
                dataType: "json",
                type: "POST",
                data: data.field,
                success: function (res) {
                    if (res.code) {
                        return layui.popup.failure(res.msg);
                    }
                    return layui.popup.success("操作成功");
                }
            });
            return false;
        });

        // 普通图片上传
        var uploadInst = upload.render({
            elem: '#test1'
            , url: '/app/storage/admin/setting/testUpload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
            , before: function (obj) {
                //预读本地文件示例，不支持ie8
                obj.preview(function (index, file, result) {
                    $('#demo1').attr('src', result); //图片链接（base64）
                });

                element.progress('demo', '0%'); //进度条复位
                // layer.msg('上传中', {icon: 16, time: 0});
            }
            , done: function (res) {
                //如果上传失败
                if (res.code > 0) {
                    $('#demo1').attr('src', '');
                    element.progress('demo', '0%');
                    $('#demoText').html(res.msg);
                    return layer.msg(res.msg, {icon: 0, time: 3000});
                }
                //上传成功的一些操作
                //……
                $('#demoText').html(''); //置空上传失败的状态
            }
            , error: function () {
                //演示失败状态，并实现重传
                let demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function () {
                    uploadInst.upload();
                });
            }
            //进度条
            , progress: function (n, elem, e) {
                console.log(elem)
                console.log(n)
                console.log(e)
                element.progress('demo', n + '%'); //可配合 layui 进度条元素使用
                if (n == 100) {
                    layer.msg('上传完毕', {icon: 1});
                }
            }
        });
    });
</script>

<style>

    .layui-input-block {
        margin-left: 150px;
    }

    .layui-form-label {
        width: 120px;
    }

</style>
</body>
</html>

