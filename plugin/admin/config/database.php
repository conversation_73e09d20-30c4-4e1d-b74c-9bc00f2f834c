<?php
return  [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'driver'      => 'mysql',
            'host'        => env('DB_HOST', 'mysql'),
            'port'        => env('DB_PORT',3306),
            'database'    => env('DB_DATABASE','ait'),
            'username'    => env('DB_USER','ait'),
            'password'    =>env('DB_PASSWORD','ait96123kjfg.'),
            'charset'     => 'utf8mb4',
            'collation'   => 'utf8mb4_general_ci',
            'prefix'      => '',
            'strict'      => true,
            'engine'      => null,
        ],
    ],
];