.project-list-item {
	background-color: #fff;
	border-radius: 4px;
	cursor: pointer;
	transition: all .2s;
}

.project-list-item:hover {
	box-shadow: 2px 0 4px rgba(0, 21, 41, .35);
}

.project-list-item .project-list-item-cover {
	width: 100%;
	height: 180px;
	display: block;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}

.project-list-item-body {
	padding: 20px;
	border: 1px solid #e8e8e8;
}

.project-list-item .project-list-item-body>h2 {
	font-size: 16px;
	color: #333;
	margin-bottom: 12px;
}

.project-list-item .project-list-item-text {
	height: 40px;
	overflow: hidden;
	margin-bottom: 12px;
}

.project-list-item .project-list-item-desc {
	position: relative;
}

.project-list-item .project-list-item-desc .time {
	color: #999;
	font-size: 12px;
}

.project-list-item .project-list-item-desc .ew-head-list {
	position: absolute;
	right: 0;
	top: 0;
}

.ew-head-list .ew-head-list-item:first-child {
	margin-left: 0;
}

.ew-head-list .ew-head-list-item {
	width: 22px;
	height: 22px;
	border-radius: 50%;
	border: 1px solid #fff;
	margin-left: -10px;
}

.ew-head-list .ew-head-list-item {
	width: 22px;
	height: 22px;
	border-radius: 50%;
	border: 1px solid #fff;
	margin-left: -10px;
}

.cloud-card-component {
	padding: 20px;
}

.cloud-card-component .layui-table-click {
	border-radius: 6px!important;
}

.ew-table-loading {
    padding: 10px 0;
    text-align: center;
}
.ew-table-loading > i {
    color: #999;
    font-size: 30px;
}
.ew-table-loading.ew-loading-float {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}