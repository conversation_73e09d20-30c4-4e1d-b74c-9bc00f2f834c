.pear-nav-tree {
	width: 230px;
	border-radius: 0px;
	background-color: #001529;
}

.pear-nav-tree .layui-nav-item>a {
	height: 56px;
	line-height: 56px;
	padding-top: 0px;
	padding-bottom: 0px;
}

.pear-nav-tree .layui-nav-item dd a {
	height: 48px;
	line-height: 48px;
}

.pear-nav-tree  .layui-nav-item>a .layui-nav-more {
	padding: 0px;
}

.pear-side-scroll::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}
.pear-side-scroll{
	width: 230px;
}

.pear-nav-tree .layui-nav-child dd.layui-this,
.layui-nav-tree .layui-nav-child dd.layui-this a,
.layui-nav-tree .layui-this,
.layui-nav-tree .layui-this>a,
.layui-nav-tree .layui-this>a:hover {
	background-color: #5FB878;
}

.pear-nav-tree .toast {
	font-size: 14px;
	margin: 5px;
	margin-right: 8px;
	text-align: center;
	height: 40px;
	line-height: 40px;
	color: lightgray;
}


.pear-nav-tree .layui-nav-item a i {
	margin-right: 12px;
}

.pear-nav-tree .layui-nav-item a span {
	letter-spacing: 2px;
	font-size: 13.5px;
}

.pear-nav-tree .layui-nav-item a:hover {
	background-color: transparent;
}

.pear-nav-tree .layui-nav-more {
	margin-right: 5px;
}

.pear-nav-tree .layui-nav-bar {
	display: none;
}

.pear-nav-tree .layui-nav-item a .layui-badge-dot {
	float: right;
	right: 13px;
}

.pear-nav-tree .layui-nav-item a .layui-badge {
	float: right;
	right: 10px;
}

/** 实 现 菜 单 隐 藏 */
.pear-nav-mini {
	overflow: hidden;
}

.pear-nav-mini .layui-nav-item a span {
	display: none;
}

.pear-nav-mini .layui-nav-child {
	display: none;
}

.pear-nav-mini .layui-nav-more {
	display: none !important;
}

.pear-nav-control.pc a {
	font-weight: 500;
	font-size: 14px;
}

.pear-nav-control.pc li{
	display: inline-block;
}

.pear-nav-control.pc .layui-nav-bar {
	top: 0px !important;
}

.pear-nav-control.pc .layui-this * {
	background-color: whitesmoke;
}

.pear-nav-control.pc *{
	color: darkslategray!important;
}

.pear-nav-control.pc .layui-nav-bar{
	display: none!important;
}

.pear-nav-control .layui-nav-child{
	border: 1px solid whitesmoke;
	border-radius: 6px;
	width: 150px;
}

/** 隐 藏 后 子 级 悬 浮 菜 单 */
.pear-nav-tree .layui-nav-hover {
	position: fixed;
	min-width: 130px;
	padding: 4px;
	display: block !important;
	background: transparent !important;
}
.pear-nav-tree .layui-nav-hover:before {
	content: '';
	position: absolute;
	right: 4px;
	left: 4px;
	bottom: 0;
	top: 0;
	border-radius: 4px;
	overflow: hidden;
	background-color: #001529;
	display: block;
	box-shadow: 0px 0px 3px lightgray;
}
.pear-nav-tree .layui-nav-hover a span {
	display: inline-block !important;
}
.pear-nav-tree .layui-nav-hover a i {
	display: none;
}
.pear-nav-tree .layui-nav-child dd a span {
	margin-left: 26px !important;
}
.pear-nav-tree .layui-nav-child dd a i {
	display: none;
}
.pear-nav-tree .layui-nav-hover dd a span {
	margin-left: 0px !important;
}
.pear-nav-tree dl {
	padding-top: 0;
	padding-bottom: 0;
}
/** 亮 样 式*/
.dark-theme .layui-nav-tree{
	background-color: #001529!important; 
}

.light-theme{
	background-color: white!important;
}

.light-theme .pear-nav-tree,
.light-theme .pear-nav-tree .layui-nav-hover:before,
.light-theme .pear-nav-tree .layui-nav-child{
	background-color: white!important;
}

.light-theme .pear-nav-tree a,
.light-theme .pear-nav-tree .layui-nav-more{
	color: dimgray!important;
	border-top-color: dimgray;
}

.light-theme .pear-nav-tree .layui-nav-itemed>a>.layui-nav-more{
	border-top-color: white!important;
	border-bottom-color: dimgray!important;
}

.light-theme .pear-nav-tree .layui-this a,
.light-theme .pear-nav-tree .layui-this{
	color: white!important;
	background-color: #5FB878!important;
	
}

.light-theme .pear-nav-tree .layui-this a:hover{
	background-color: #5FB878!important;
	
}
 
.light-theme .pear-nav-tree .layui-nav-bar{
	display: none;
	
}

/** 下 拉 图 标 */
.pear-nav-tree.arrow .layui-nav-more {
	font-family: layui-icon !important;
	font-size: 10px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	overflow: hidden;
	width: auto;
	height: auto;
	line-height: normal;
	border: none;
	top: 23px;
	margin-right: 2px !important;
	margin: 0;
	padding: 0;
	display: inline-block;
	transition: all .2s;
	-webkit-transition: all .2s;
}

.pear-nav-tree.arrow .layui-nav-child .layui-nav-more {
	top: 17px;
}

.pear-nav-tree.arrow .layui-nav-more:before {
	content: "\e61a";
}

.pear-nav-tree.arrow .layui-nav-itemed>a>.layui-nav-more {
	transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
	-webkit-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	width: 12px;
	text-align: center;
}
.pear-nav-tree.arrow .layui-nav-child.layui-nav-hover>dd>a>.layui-nav-more {
	display: inline-block !important;
	transform: rotate(270deg);
	-ms-transform: rotate(270deg);
	-moz-transform: rotate(270deg);
	-webkit-transform: rotate(270deg);
	-o-transform: rotate(270deg);
	width: 12px;
	text-align: center;
	background-color: transparent !important;
}

.pear-nav-tree.arrow .layui-nav-child.layui-nav-hover>a>.layui-nav-more:before,
.pear-nav-tree.arrow .layui-nav-itemed>a>.layui-nav-more:before {
	content: '\e61a';
	display: inline-block;
	vertical-align: middle;
}
