.iziToast-capsule {
    font-size: 0;
    height: 0;
    width: 100%;
    transform: translateZ(0);
    backface-visibility: hidden;
    transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1), height 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.iziToast-capsule, .iziToast-capsule * {
    box-sizing: border-box;
}

.iziToast-overlay {
    display: block;
    position: fixed;
    top: -100px;
    left: 0;
    right: 0;
    bottom: -100px;
    z-index: 19891100;
}

.iziToast {
    display: inline-block;
    clear: both;
    position: relative;
    font-family: 'Lato', Tahoma, Arial;
    font-size: 14px;
    padding: 8px 45px 9px 0;
    background: #fff;
    border-color: #fff;
    width: 100%;
    pointer-events: all;
    cursor: default;
    transform: translateX(0);
    -webkit-touch-callout: none /* iOS Safari */;
    -webkit-user-select: none /* Chrome/Safari/Opera */;
    -khtml-user-select: none /* Konqueror */;
    -moz-user-select: none /* Firefox */;
    -ms-user-select: none /* Internet Explorer/Edge */;
    user-select: none;
    min-height: 45px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    max-width: 90%;
    margin: 5px 0;
    border-radius: 4px;
}

.iziToast > .iziToast-progressbar {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
    background: rgba(255, 255, 255, 0.5);
}

.iziToast > .iziToast-progressbar > div {
    height: 2px;
    width: 100%;
    background: #dddddd;
    border-radius: 0 0 3px 3px;
}

.iziToast.iziToast-theme-dark > .iziToast-progressbar > div {
    background: #fff;
}

.iziToast.iziToast-balloon:before {
    content: '';
    position: absolute;
    right: 8px;
    left: auto;
    width: 0px;
    height: 0px;
    top: 100%;
    border-right: 0px solid transparent;
    border-left: 15px solid transparent;
    border-top: 10px solid #000;
    border-top-color: inherit;
    border-radius: 0;
}

.iziToast.iziToast-balloon .iziToast-progressbar {
    top: 0;
    bottom: auto;
}

.iziToast.iziToast-balloon > div {
    border-radius: 0 0 0 3px;
}

.iziToast > .iziToast-cover {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    margin: 0;
    background-size: 100%;
    background-position: 50% 50%;
    background-repeat: no-repeat;
}

.iziToast > .iziToast-close {
    position: absolute;
    right: 0;
    top: 0;
    border: 0;
    padding: 0;
    opacity: 0.6;
    width: 42px;
    height: 100%;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAJPAAACTwBcGfW0QAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAD3SURBVFiF1ZdtDoMgDEBfdi4PwAX8vLFn0qT7wxantojKupmQmCi8R4tSACpgjC2ICCUbEBa8ingjsU1AXRBeR8aLN64FiknswN8CYefBBDQ3whuFESy7WyQMeC0ipEI0A+0FeBvHUFN8xPaUhAH/iKoWsnXHGegy4J0yxialOfaHJAz4bhRzQzgDvdGnz4GbAonZbCQMuBm1K/kcFu8Mp1N2cFFpsxsMuJqqbIGExGl4loARajU1twskJLLhIsID7+tvUoDnIjTg5T9DPH9EBrz8rxjPzciAl9+O8SxI8CzJ8CxKFfh3ynK8Dyb8wNHM/XDqejx/AtNyPO87tNybAAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 8px;
    cursor: pointer;
    outline: none;
}

.iziToast > .iziToast-close:hover {
    opacity: 1;
}

.iziToast > .iziToast-body {
    position: relative;
    padding: 0 0 0 10px;
    height: auto;
    min-height: 28px;
    margin: 0 0 0 15px;
    text-align: left;
}

.iziToast > .iziToast-body:after {
    content: "";
    display: table;
    clear: both;
}

.iziToast > .iziToast-body .iziToast-texts {
    margin: 6px 0;
    padding-right: 2px;
    display: inline-block;
    float: left;
}

.iziToast > .iziToast-body .iziToast-inputs {
    min-height: 19px;
    float: left;
    margin: 3px -2px;
}

.iziToast > .iziToast-body .iziToast-inputs > input:not([type=checkbox]):not([type=radio]),
.iziToast > .iziToast-body .iziToast-inputs > select {
    position: relative;
    display: inline-block;
    margin: 2px;
    border-radius: 2px;
    border: 0;
    padding: 4px 7px;
    font-size: 13px;
    letter-spacing: 0.02em;
    background: rgba(0, 0, 0, 0.1);
    color: #000;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
    min-height: 26px;
}

.iziToast > .iziToast-body .iziToast-inputs > input:not([type=checkbox]):not([type=radio]):focus,
.iziToast > .iziToast-body .iziToast-inputs > select:focus {
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.6);
}

.iziToast > .iziToast-body .iziToast-buttons {
    min-height: 17px;
    float: left;
    margin: 4px 0 0 0;
}

.iziToast > .iziToast-body .iziToast-buttons > a,
.iziToast > .iziToast-body .iziToast-buttons > button,
.iziToast > .iziToast-body .iziToast-buttons > input:not([type=checkbox]):not([type=radio]) {
    position: relative;
    display: inline-block;
    margin: 2px;
    border-radius: 2px;
    border: 0;
    padding: 5px 10px;
    font-size: 12px;
    letter-spacing: 0.02em;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.1);
    color: #555;
}

.iziToast > .iziToast-body .iziToast-buttons > a:hover,
.iziToast > .iziToast-body .iziToast-buttons > button:hover,
.iziToast > .iziToast-body .iziToast-buttons > input:not([type=checkbox]):not([type=radio]):hover {
    background: rgba(0, 0, 0, 0.15);
}

.iziToast > .iziToast-body .iziToast-buttons > a:focus,
.iziToast > .iziToast-body .iziToast-buttons > button:focus,
.iziToast > .iziToast-body .iziToast-buttons > input:not([type=checkbox]):not([type=radio]):focus {
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.6);
}

.iziToast > .iziToast-body .iziToast-buttons > a:active,
.iziToast > .iziToast-body .iziToast-buttons > button:active,
.iziToast > .iziToast-body .iziToast-buttons > input:not([type=checkbox]):not([type=radio]):active {
    top: 1px;
}

.iziToast > .iziToast-body .iziToast-icon {
    height: 100%;
    position: absolute;
    left: 0;
    top: 50%;
    display: table;
    font-size: 23px;
    line-height: 24px;
    margin-top: -11px;
    color: #000;
    width: 24px;
    height: 24px;
}

.iziToast > .iziToast-body .iziToast-icon.ico-info {
    background: url("data:image/svg+xml;base64,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") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast > .iziToast-body .iziToast-icon.ico-warning {
    background: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTU1NzcwODczNzUzIiBjbGFzcz0iaWNvbiIgc3R5bGU9IiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjUxMTgiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNTEyLjAyMzI3MyAxMDI0Qzc5NC43NjM2MzYgMTAyNCAxMDI0IDc5NC43ODY5MDkgMTAyNCA1MTEuOTc2NzI3IDEwMjQgMjI5LjIzNjM2NCA3OTQuNzg2OTA5LTAuMDIzMjczIDUxMi4wMjMyNzMtMC4wMjMyNzNTMCAyMjkuMjEzMDkxIDAgNTExLjk3NjcyN0MwIDc5NC43ODY5MDkgMjI5LjIzNjM2NCAxMDI0IDUxMi4wMjMyNzMgMTAyNE00NjkuMzQxMDkxIDc2OGMwLTIzLjU1MiAxOC45MjA3MjctNDIuNjU4OTA5IDQyLjcwNTQ1NS00Mi42NTg5MDkgMjMuNTUyIDAgNDIuNjU4OTA5IDE4LjkyMDcyNyA0Mi42NTg5MDkgNDIuNjU4OTA5IDAgMjMuNTc1MjczLTE4LjkyMDcyNyA0Mi43MDU0NTUtNDIuNjU4OTA5IDQyLjcwNTQ1NUM0ODguNDcxMjczIDgxMC43MDU0NTUgNDY5LjM0MTA5MSA3OTEuNzM4MTgyIDQ2OS4zNDEwOTEgNzY4TTQ2OS4zNDEwOTEgMjU1LjgxMzgxOGMwLTIzLjQ1ODkwOSAxOC45MjA3MjctNDIuNDcyNzI3IDQyLjcwNTQ1NS00Mi40NzI3MjcgMjMuNTUyIDAgNDIuNjU4OTA5IDE5LjQzMjcyNyA0Mi42NTg5MDkgNDIuNDcyNzI3bDAgMzQxLjczNjcyN2MwIDIzLjQ1ODkwOS0xOC45MjA3MjcgNDIuNDcyNzI3LTQyLjY1ODkwOSA0Mi40NzI3MjctMjMuNTc1MjczIDAtNDIuNzA1NDU1LTE5LjQzMjcyNy00Mi43MDU0NTUtNDIuNDcyNzI3TDQ2OS4zNDEwOTEgMjU1LjgxMzgxOHoiIHAtaWQ9IjUxMTkiIGZpbGw9IiNmZjk5MDAiPjwvcGF0aD48L3N2Zz4=") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast > .iziToast-body .iziToast-icon.ico-error {
    background: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTU1NzcwNDI1NDkwIiBjbGFzcz0iaWNvbiIgc3R5bGU9IiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjEyNjUiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNTExLjk5NjQxOCAyMy41MjA2OTNjLTI2OS43NjcwNSAwLTQ4OC40NTQ3NDggMjE4LjY4ODcyMS00ODguNDU0NzQ4IDQ4OC40NTQ3NDggMCAyNjkuNzY4MDczIDIxOC42ODg3MjEgNDg4LjQ1MzcyNCA0ODguNDU0NzQ4IDQ4OC40NTM3MjQgMjY5Ljc2NjAyNyAwIDQ4OC40NTQ3NDgtMjE4LjY4NTY1MSA0ODguNDU0NzQ4LTQ4OC40NTM3MjRDMTAwMC40NTExNjYgMjQyLjIwOTQxNCA3ODEuNzYyNDQ1IDIzLjUyMDY5MyA1MTEuOTk2NDE4IDIzLjUyMDY5M3pNNzY5LjMzNDM5MSA3MTUuMTU1OTU2bC01NC4xNTY0MzQgNTQuMTU2NDM0TDUxMS45OTY0MTggNTY2LjEyOTgyOCAzMDguODEzODU2IDc2OS4zMTIzOWwtNTQuMTU1NDExLTU0LjE1NjQzNEw0NTcuODQxMDA4IDUxMS45NzU0NDEgMjU0LjY1ODQ0NiAzMDguNzkyODc4bDU0LjE1NTQxMS01NC4xNTU0MTFMNTExLjk5NjQxOCA0NTcuODIwMDNsMjAzLjE4MjU2Mi0yMDMuMTgyNTYyIDU0LjE1NjQzNCA1NC4xNTU0MTFMNTY2LjE1MTgyOSA1MTEuOTc1NDQxIDc2OS4zMzQzOTEgNzE1LjE1NTk1NnoiIHAtaWQ9IjEyNjYiIGZpbGw9IiNGNTZDNkMiPjwvcGF0aD48L3N2Zz4=") no-repeat 50% 50%;
    background-size: 80%;
}

.iziToast > .iziToast-body .iziToast-icon.ico-success {
    background: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTU1NzcwNTI2MjA3IiBjbGFzcz0iaWNvbiIgc3R5bGU9IiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ5NjUiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNMTAyNCA1MTEuOTk5NTAyYzAgMjgyLjc3MDU1Ny0yMjkuMjI5OTQxIDUxMi4wMDA0OTgtNTExLjk5OTUwMiA1MTIuMDAwNDk4LTI4Mi43Njk1NjEgMC01MTIuMDAwNDk4LTIyOS4yMjk5NDEtNTEyLjAwMDQ5OC01MTIuMDAwNDk4IDAtMjgyLjc2ODU2NiAyMjkuMjMwOTM2LTUxMS45OTk1MDIgNTEyLjAwMDQ5OC01MTEuOTk5NTAyQzc5NC43NjkwNjQgMCAxMDI0IDIyOS4yMzA5MzYgMTAyNCA1MTEuOTk5NTAyek03OTYuNTc3MDgxIDM3OC4zMTc3MTNsLTQ4LjQ1OTA1MS00OC40NTkwNTEtMjk3LjA3ODM4NyAyOTcuMDc3MzkyTDI3Ny4yNjc4NTIgNDUzLjE2NTI1OGwtNDguNDU4MDU2IDQ4LjQ1ODA1NiAyMjIuMjI4ODUxIDIyMi4yMzA4NDJMNzk2LjU3NzA4MSAzNzguMzE3NzEzeiIgcC1pZD0iNDk2NiIgZmlsbD0iIzY3YzIzYSI+PC9wYXRoPjwvc3ZnPg==") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast > .iziToast-body .iziToast-icon.ico-question {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAQAAAAAYLlVAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAJcEhZcwAADdcAAA3XAUIom3gAAAAHdElNRQfhCQkUEhFovxTxAAAEDklEQVRo3s2ZTWgTQRTHf03ipTRUqghNSgsRjHgQrFUQC6JgD1Kak3gQUUoPqRdBglf1oBehBws9Cn4cGk+1SOmh2upBxAYVoeJHrR9tgq0i1Cq0lqYeks7MbpPdmU00/c8hm9n33v/t7Nt5M2+qMEWQI0QIibZKRrQpHvLL2KI2wnQzzBKrDm2RIeKEy01dTYKUI7G1ZRknQXV5yP10kTYgly1NF/5S6duZ8ES+1iZodyaocrjXxE0OFeifYYgp0mRIkwFChAkRJsIxGgrIP+I0n82fvZW5dc/zkss0O2o1c5mX6/TmaDWl77RFe5YkUW3tKEmyFv0lOvXJ/fTYnmCEFuMRbGHEZqVHLyT9DFjUJmkzJl9DG5MWWwM6Llif/gF1nukB6nhgGwUXdFrE+wiURA8QoM9i0zEWWpXQW+ZsyeRrOMuyEo5Fv4gmy4dXPvqcC+pH2VRYaMwy+OWG+iLGCgm0W0Kv9HdvR8ASjmKCXpuK/bxiV/76A/v5UdDIZuKcJGjrnec5KZ7wwsWFOp6xPX/9mt2sqDe7FO+Kf/fXHBPPDWpdXGhTpLvUG9VKwh1xMDDjkvu+cNDFBTk7ptX1QkKZ850m3duu6fcrWxwdaFFyREJ2j4vOpKP6Du6z4uJCv8sYJIVkCnJBGGZaBONO3roY2EqNrSfIPi7SKP4fdXyNUd6I6wbSAHEl33tFLe+FlSsusnK90A0+oEPcuufZgXnOi+u9LrKSJQZQw6LwqBnv2CKsfHORbFbyQhA6xN/pEuihSdj56Co7LWRjPiKie6gkB2LiKuUqK5kiPkLiz1QJ9K1cNXBAMoUCigNpQ9IqDtMI1HKA4/jyvUsaoSyZLA5kjOjDPFZen8Ql5TsvBskUgjciIPSX3QAXC86DT7VWvlEh/xZ+ij9BDVWJ0QL0SbZq6QaFxoLPcXPmBLveLCc4wXdDK6s+6/vwhCSniFLPXW0NJe5UB8zKCsviqpc7vGPVQFcyZbyPwGD+d5ZnxmNWlhG4xSBZZjivjIWHEQgoDkSMjMwTo54569JSE5IpA7EyJSMTyGTUAUFlO1ZKOtaHTMeL1PhYYFTcihmY2cQ5+ullj7EDkiVfVez2sCTz8yiv84djhg7IJVk81xFWJlPdfHBG0flkRC/zQFZ+DSllNtfDdUsOMCliyGX5uOzU3ZhIXFDof4m1gDuKbEx0t2YS25gVGpcMnr/I1kx3c6piB8P8ZoqEwfMX3ZyCXynJTmq/U7NUXqfUzCbWL1wqVKBQUeESzQYoUlW8TAcVL1RCxUu1G6BYXfFyfQ4VPbDI4T8d2WzgQ6sc/vmxnTsqfHCZQzUJxm1h5dxS5Tu6lQgTZ0ipqRVqSwzTbbLHMt+c19iO76tsx/cLZub+Ali+tYC93olEAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTA5LTA5VDIwOjE4OjE3KzAyOjAwjKtfjgAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wOS0wOVQyMDoxODoxNyswMjowMP325zIAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast > .iziToast-body .iziToast-icon.ico-load {
    background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTQ1NTUyMzAyOTE3IiBjbGFzcz0iaWNvbiIgc3R5bGU9IiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQyNDAiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNTEyIDY0YzI0Ny4yIDAgNDQ4IDIwMC44IDQ0OCA0NDhoLTY0YzAtMjEyLTE3Mi0zODQtMzg0LTM4NFY2NHogbTAgODMyYy0yMTIgMC0zODQtMTcyLTM4NC0zODRINjRjMCAyNDcuMiAyMDAuOCA0NDggNDQ4IDQ0OHYtNjR6IiBwLWlkPSI0MjQxIiBmaWxsPSIjMUU5RkZGIj48L3BhdGg+PC9zdmc+') no-repeat 50% 50%;
    background-size: 85%;
    animation: rotating 1.2s linear infinite
}

@keyframes rotating {
    from {
        transform: rotate(0)
    }
    to {
        transform: rotate(360deg)
    }
}

.iziToast > .iziToast-body .iziToast-title {
    padding: 0;
    margin: 0 0 10px 0;
    line-height: 16px;
    font-size: 14px;
    text-align: left;
    float: left;
    color: #303133;
    white-space: normal;
}

.iziToast > .iziToast-body .iziToast-message {
    padding: 0;
    margin: 0;
    font-size: 14px;
    line-height: 16px;
    text-align: left;
    float: left;
    color: #606266;
    white-space: normal;
}

.iziToast.iziToast-animateInside .iziToast-title,
.iziToast.iziToast-animateInside .iziToast-message,
.iziToast.iziToast-animateInside .iziToast-icon,
.iziToast.iziToast-animateInside .iziToast-buttons-child,
.iziToast.iziToast-animateInside .iziToast-inputs-child {
    opacity: 0;
}

.iziToast-target {
    position: relative;
    width: 100%;
    margin: 0 auto;
}

.iziToast-target .iziToast-capsule {
    overflow: hidden;
}

.iziToast-target .iziToast-capsule:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

.iziToast-target .iziToast-capsule .iziToast {
    width: 100%;
    float: left;
}

.iziToast-wrapper {
    z-index: 19891101;
    position: fixed;
    width: 100%;
    pointer-events: none;
    display: flex;
    flex-direction: column;
}

.iziToast-wrapper .iziToast.iziToast-balloon:before {
    border-right: 0 solid transparent;
    border-left: 15px solid transparent;
    border-top: 10px solid #000;
    border-top-color: inherit;
    right: 8px;
    left: auto;
}

.iziToast-wrapper-bottomLeft {
    left: 0;
    bottom: 0;
    text-align: left;
}

.iziToast-wrapper-bottomLeft .iziToast.iziToast-balloon:before {
    border-right: 15px solid transparent;
    border-left: 0 solid transparent;
    right: auto;
    left: 8px;
}

.iziToast-wrapper-bottomRight {
    right: 0;
    bottom: 0;
    text-align: right;
}

.iziToast-wrapper-topLeft {
    left: 0;
    top: 0;
    text-align: left;
}

.iziToast-wrapper-topLeft .iziToast.iziToast-balloon:before {
    border-right: 15px solid transparent;
    border-left: 0 solid transparent;
    right: auto;
    left: 8px;
}

.iziToast-wrapper-topRight {
    top: 0;
    right: 0;
    text-align: right;
}

.iziToast-wrapper-topCenter {
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
}

.iziToast-wrapper-bottomCenter {
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
}

.iziToast-wrapper-center {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    justify-content: center;
    flex-flow: column;
    align-items: center;
}

.iziToast-rtl {
    direction: rtl;
    padding: 8px 0 9px 45px;
    font-family: Tahoma, 'Lato', Arial;
}

.iziToast-rtl .iziToast-cover {
    left: auto;
    right: 0;
}

.iziToast-rtl .iziToast-close {
    right: auto;
    left: 0;
}

.iziToast-rtl .iziToast-body {
    padding: 0 10px 0 0;
    margin: 0 16px 0 0;
    text-align: right;
}

.iziToast-rtl .iziToast-body .iziToast-buttons,
.iziToast-rtl .iziToast-body .iziToast-inputs,
.iziToast-rtl .iziToast-body .iziToast-texts,
.iziToast-rtl .iziToast-body .iziToast-title,
.iziToast-rtl .iziToast-body .iziToast-message {
    float: right;
    text-align: right;
}

.iziToast-rtl .iziToast-body .iziToast-icon {
    left: auto;
    right: 0;
}

@media only screen and (min-width: 568px) {
    .iziToast-wrapper {
        padding: 10px 15px;
    }

    .iziToast {
        width: auto;
    }

    .iziToast:not(.iziToast-rtl) .iziToast-cover {
        border-radius: 3px 0 0 3px;
    }

    .iziToast.iziToast-rtl .iziToast-cover {
        border-radius: 0 3px 3px 0;
    }

    .iziToast.iziToast-color-dark:after {
        box-shadow: inset 0 -10px 20px -10px rgba(255, 255, 255, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.25);
    }

    .iziToast.iziToast-balloon .iziToast-progressbar {
        background: transparent;
    }

    .iziToast.iziToast-balloon:after {
        box-shadow: 0 10px 10px -5px rgba(0, 0, 0, 0.25), inset 0 10px 20px -5px rgba(0, 0, 0, 0.25);
    }

    .iziToast-target .iziToast:after {
        box-shadow: inset 0 -10px 20px -10px rgba(0, 0, 0, 0.2), inset 0 0 5px rgba(0, 0, 0, 0.1);
    }
}

.iziToast.iziToast-theme-dark {
    background: #565c70;
    border-color: #565c70;
}

.iziToast.iziToast-theme-dark .iziToast-title {
    color: #fff;
}

.iziToast.iziToast-theme-dark .iziToast-message {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
}

.iziToast.iziToast-theme-dark .iziToast-close {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAJcEhZcwAADdcAAA3XAUIom3gAAAAHdElNRQfgCR4OIQIPSao6AAAAwElEQVRIx72VUQ6EIAwFmz2XB+AConhjzqTJ7JeGKhLYlyx/BGdoBVpjIpMJNjgIZDKTkQHYmYfwmR2AfAqGFBcO2QjXZCd24bEggvd1KBx+xlwoDpYmvnBUUy68DYXD77ESr8WDtYqvxRex7a8oHP4Wo1Mkt5I68Mc+qYqv1h5OsZmZsQ3gj/02h6cO/KEYx29hu3R+VTTwz6D3TymIP1E8RvEiiVdZfEzicxYLiljSxKIqlnW5seitTW6uYnv/Aqh4whX3mEUrAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE2LTA5LTMwVDE0OjMzOjAyKzAyOjAwl6RMVgAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNi0wOS0zMFQxNDozMzowMiswMjowMOb59OoAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 8px;
}

.iziToast.iziToast-theme-dark .iziToast-icon {
    color: #fff;
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-info {
    background: url("data:image/svg+xml;base64,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") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-warning {
    background: url("data:image/svg+xml;base64,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") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-error {
    background: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTQxNjUxMzE1NTQ3IiBjbGFzcz0iaWNvbiIgc3R5bGU9IiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjY4MDYiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNzcyLjI2NjY2NyA4ODMuMmMyNS42IDI1LjYgNjQgMjUuNiA4OS42IDAgMjUuNi0yNS42IDI1LjYtNjQgMC04OS42TDIzMC40IDE2Mi4xMzMzMzNjLTI1LjYtMjUuNi02NC0yNS42LTg5LjYgMC0yNS42IDI1LjYtMjUuNiA2NCAwIDg5LjZsNjMxLjQ2NjY2NyA2MzEuNDY2NjY3eiIgcC1pZD0iNjgwNyIgZmlsbD0iI2ZmZmZmZiI+PC9wYXRoPjxwYXRoIGQ9Ik04NjEuODY2NjY3IDI1MS43MzMzMzNjMjUuNi0yNS42IDI1LjYtNjQgMC04OS42LTI1LjYtMjUuNi02NC0yNS42LTg5LjYgMEwxNDAuOCA3OTMuNmMtMjUuNiAyNS42LTI1LjYgNjQgMCA4OS42czY0IDI1LjYgODkuNiAwbDYzMS40NjY2NjctNjMxLjQ2NjY2N3oiIHAtaWQ9IjY4MDgiIGZpbGw9IiNmZmZmZmYiPjwvcGF0aD48L3N2Zz4=") no-repeat 50% 50%;
    background-size: 80%;
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-success {
    background: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTQxNjUxMjQyNTMxIiBjbGFzcz0iaWNvbiIgc3R5bGU9IiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjYwMjMiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCI+PGRlZnM+PHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPjwvZGVmcz48cGF0aCBkPSJNNDA0LjI1MTQyOCA4MDkuNjEwN2MtMTEuNjY0MTAyIDAtMjMuMzI3MTc5LTQuNDQ4NzgyLTMyLjIyNTc2Ny0xMy4zNDczNjlMODMuOTgwMDk4IDUwOC4yMTk4MTVjLTE3Ljc5ODE5OS0xNy43OTcxNzUtMTcuNzk4MTk5LTQ2LjY1MzMzNSAwLTY0LjQ1MTUzM3M0Ni42NTMzMzUtMTcuNzk4MTk5IDY0LjQ1MTUzNCAwbDI4OC4wNDQ1MzkgMjg4LjA0MjQ5MWMxNy43OTgxOTkgMTcuNzk4MTk5IDE3Ljc5ODE5OSA0Ni42NTQzNTkgMCA2NC40NTI1NTgtOC44OTc1NjMgOC44OTg1ODctMjAuNTYxNjY1IDEzLjM0NzM2OS0zMi4yMjQ3NDMgMTMuMzQ3MzY5eiIgcC1pZD0iNjAyNCIgZmlsbD0iI2ZmZmZmZiI+PC9wYXRoPjxwYXRoIGQ9Ik00MDQuMjczOTUzIDgwOS42MTE3MjNjLTExLjY2NDEwMiAwLTIzLjMyNjE1NS00LjQ0OTgwNi0zMi4yMjU3NjYtMTMuMzQ4MzkyLTE3Ljc5ODE5OS0xNy43OTcxNzUtMTcuNzk5MjIyLTQ2LjY1MzMzNS0wLjAwMTAyNC02NC40NTE1MzRMODc2LjEwMTgxMyAyMjcuNzM1NjQ2YzE3Ljc5NjE1MS0xNy43OTgxOTkgNDYuNjUzMzM1LTE3Ljc5OTIyMiA2NC40NTE1MzMtMC4wMDEwMjQgMTcuNzk4MTk5IDE3Ljc5NzE3NSAxNy43OTkyMjIgNDYuNjUzMzM1IDAuMDAxMDI0IDY0LjQ1MTUzM0w0MzYuNTAwNzQ0IDc5Ni4yNjIzMDdjLTguODk4NTg3IDguODk4NTg3LTIwLjU2MjY4OSAxMy4zNDk0MTctMzIuMjI2NzkxIDEzLjM0OTQxNnoiIHAtaWQ9IjYwMjUiIGZpbGw9IiNmZmZmZmYiPjwvcGF0aD48L3N2Zz4=") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast.iziToast-theme-dark .iziToast-icon.ico-question {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAQAAAAAYLlVAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAKqNIzIAAAAJcEhZcwAADdcAAA3XAUIom3gAAAAHdElNRQfhCQkUEg18vki+AAAETUlEQVRo3s1ZTWhbRxD+VlIuxsLFCYVIIQYVopBDoK5bKDWUBupDMNbJ5FBKg/FBziUQdE9yaC+FHBrwsdCfQ9RTGoLxwWl+DqHEojUFFydxnB9bInZDqOsErBrr6yGvs/ueX97bldTKo4Pe7puZb3Z33s7srIIjMY1jyCEjP6ImvyX8pF64arSHznKC06wzijY5xSKz7YbuYokV2lODsyyxqz3gSY6z6gCuqcpxJluFH+Z8U+D/0jyHoxFUBHgfvsGHIS9WMIUlVFFDFTUAGWSRQRY5HMeBEP6b+Ew9dh/7INd2jGeO59kfKdXP85zbIbfGQVf4sYC3N1hm3lo6zzIbPvk6x+zBk7wQGMEMB5xncIAzAS0XrFySSV72iS1yyBVcdA1x0afrsoUJgdFfY2+z8ADAXl7zz0KcwJiPfZKpVuABgClO+nRG+QIHDdfb4qlWwUXvKW4Z7vi6L4J9vg+vbfCeCeZH2RfOdMOc/HbCA4BvIW6EMQz7XK/ltd+hP+VzR9mgva2YSfyGI17fA7ynnocqeQNFfIJ0oHsdv6CC2+rXGBN6cQdveY3fcVRtmy/HDete+93zy8jA8zV7YkwYMrjHzRddRsCdiVCwwmh6wg9iTNC7Y9XIF1iS7kbUpsvvGEdPuTfSgAEjRpR096x0liPFD/Eqt2NMuBQzB2XhrACAApjFsuQFh9XdGAX70B3oSuNdnMVBaX+sopYxjwVpHFBVACyKTXNoktjD+6Ll8xhenS9MAAkAI/Lux2YNUOs4I413Ypg1SgEAu7kpFvWjaeJe0fJHDGe/cNaZBkekudw8PMA+0fMwlndZeAsJ5KR/qhUDUJCnSiyvRsolkJHGUgvjH8QXDgZopEzKMKDqCKrwEQ4C6MH7GEXC665buLJG8hlQc4LP4paxfJrOqYVYYY2UARfEIazTbgDg2dB98GebzJd54b8L/iWNdLyooeR6CHyZ+6xk0yKxkYg6nEVSUG4VJ9QJ9cxRCxO+9WiOyvgUeexXP1hLGH5nGuBWVtiSp4vqe3VP0UFWI9Wan4Er3v8q7jjPWVtm4FtcQQMrOKO2nOQCM5AyDMi56FDrKHA/1nyppS1ppBpYaE8wciEjGI2AaeM41kI4doDX4XiT3Qm1gevyruCgZg9P8xIv8m1nCzTKq6oiJ9xTMiZ505P5m8cdZ0CnZMVXHVljM7WMBzxpyDxygtdxoCEFTaMIWbZU85UvBjgUMYy0fBaAF8V1Lj9qWQ1aMZ5f4k9r+AGMSkMP1vZoZih6k6sicc5h/OFHM9vDqU/VIU7zJZdYYsKGH4g4nAJMGiXZRds1pVMoZ69RM5vfkbh0qkBhsnS2RLMLilQdL9MBHS9UAh0v1e6CYnXHy/WeeCcvLDwl/9OVze69tPKM+M+v7eJN6OzFpWdEF0ucDbhVNFXadnVrmJFlkVNGTS2M6pzmhMvltfPhnN2B63sVuL7fcNP3D1TSk2ihosPrAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTA5LTA5VDIwOjE4OjEzKzAyOjAweOR7nQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wOS0wOVQyMDoxODoxMyswMjowMAm5wyEAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAAAElFTkSuQmCC") no-repeat 50% 50%;
    background-size: 85%;
}

.iziToast.iziToast-theme-dark .iziToast-buttons > a,
.iziToast.iziToast-theme-dark .iziToast-buttons > button,
.iziToast.iziToast-theme-dark .iziToast-buttons > input {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}

.iziToast.iziToast-theme-dark .iziToast-buttons > a:hover,
.iziToast.iziToast-theme-dark .iziToast-buttons > button:hover,
.iziToast.iziToast-theme-dark .iziToast-buttons > input:hover {
    background: rgba(255, 255, 255, 0.2);
}

.iziToast.iziToast-theme-dark .iziToast-buttons > a:focus,
.iziToast.iziToast-theme-dark .iziToast-buttons > button:focus,
.iziToast.iziToast-theme-dark .iziToast-buttons > input:focus {
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.6);
}

.iziToast.iziToast-theme-dark.iziToast-color-red {
    background: #F56C6C;
    border-color: #F56C6C;
}

.iziToast.iziToast-theme-dark.iziToast-color-orange {
    background: #E6A23C;
    border-color: #E6A23C;
}

.iziToast.iziToast-theme-dark.iziToast-color-yellow {
    background: rgba(255, 249, 178, 0.9);
    border-color: rgba(255, 249, 178, 0.9);
}

.iziToast.iziToast-theme-dark.iziToast-color-blue {
    background: #409EFF;
    border-color: #409EFF;
}

.iziToast.iziToast-theme-dark.iziToast-color-green {
    background: #67C23A;
    border-color: #67C23A;
}

.iziToast.iziToast-layout2 .iziToast-body .iziToast-texts,
.iziToast.iziToast-layout2 .iziToast-body .iziToast-message {
    width: 100%;
}

.iziToast.iziToast-layout3 {
    border-radius: 2px;
}

.iziToast.iziToast-layout3::after {
    display: none;
}

.iziToast.revealIn,
.iziToast .revealIn {
    -webkit-animation: iziT-revealIn 1s cubic-bezier(0.25, 1.6, 0.25, 1) both;
    -moz-animation: iziT-revealIn 1s cubic-bezier(0.25, 1.6, 0.25, 1) both;
    animation: iziT-revealIn 1s cubic-bezier(0.25, 1.6, 0.25, 1) both;
}

.iziToast.slideIn,
.iziToast .slideIn {
    -webkit-animation: iziT-slideIn 1s cubic-bezier(0.16, 0.81, 0.32, 1) both;
    -moz-animation: iziT-slideIn 1s cubic-bezier(0.16, 0.81, 0.32, 1) both;
    animation: iziT-slideIn 1s cubic-bezier(0.16, 0.81, 0.32, 1) both;
}

.iziToast.bounceInLeft {
    -webkit-animation: iziT-bounceInLeft 0.7s ease-in-out both;
    animation: iziT-bounceInLeft 0.7s ease-in-out both;
}

.iziToast.bounceInRight {
    -webkit-animation: iziT-bounceInRight 0.85s ease-in-out both;
    animation: iziT-bounceInRight 0.85s ease-in-out both;
}

.iziToast.bounceInDown {
    -webkit-animation: iziT-bounceInDown 0.7s ease-in-out both;
    animation: iziT-bounceInDown 0.7s ease-in-out both;
}

.iziToast.bounceInUp {
    -webkit-animation: iziT-bounceInUp 0.7s ease-in-out both;
    animation: iziT-bounceInUp 0.7s ease-in-out both;
}

.iziToast.fadeIn,
.iziToast .fadeIn {
    -webkit-animation: iziT-fadeIn 0.5s ease both;
    animation: iziT-fadeIn 0.5s ease both;
}

.iziToast.fadeInUp {
    -webkit-animation: iziT-fadeInUp 0.7s ease both;
    animation: iziT-fadeInUp 0.7s ease both;
}

.iziToast.fadeInDown {
    -webkit-animation: iziT-fadeInDown 0.7s ease both;
    animation: iziT-fadeInDown 0.7s ease both;
}

.iziToast.fadeInLeft {
    -webkit-animation: iziT-fadeInLeft 0.85s cubic-bezier(0.25, 0.8, 0.25, 1) both;
    animation: iziT-fadeInLeft 0.85s cubic-bezier(0.25, 0.8, 0.25, 1) both;
}

.iziToast.fadeInRight {
    -webkit-animation: iziT-fadeInRight 0.85s cubic-bezier(0.25, 0.8, 0.25, 1) both;
    animation: iziT-fadeInRight 0.85s cubic-bezier(0.25, 0.8, 0.25, 1) both;
}

.iziToast.flipInX {
    -webkit-animation: iziT-flipInX 0.85s cubic-bezier(0.35, 0, 0.25, 1) both;
    animation: iziT-flipInX 0.85s cubic-bezier(0.35, 0, 0.25, 1) both;
}

.iziToast.fadeOut {
    -webkit-animation: iziT-fadeOut 0.7s ease both;
    animation: iziT-fadeOut 0.7s ease both;
}

.iziToast.fadeOutDown {
    -webkit-animation: iziT-fadeOutDown 0.7s cubic-bezier(0.4, 0.45, 0.15, 0.91) both;
    animation: iziT-fadeOutDown 0.7s cubic-bezier(0.4, 0.45, 0.15, 0.91) both;
}

.iziToast.fadeOutUp {
    -webkit-animation: iziT-fadeOutUp 0.7s cubic-bezier(0.4, 0.45, 0.15, 0.91) both;
    animation: iziT-fadeOutUp 0.7s cubic-bezier(0.4, 0.45, 0.15, 0.91) both;
}

.iziToast.fadeOutLeft {
    -webkit-animation: iziT-fadeOutLeft 0.5s ease both;
    animation: iziT-fadeOutLeft 0.5s ease both;
}

.iziToast.fadeOutRight {
    -webkit-animation: iziT-fadeOutRight 0.5s ease both;
    animation: iziT-fadeOutRight 0.5s ease both;
}

.iziToast.flipOutX {
    -webkit-backface-visibility: visible !important;
    backface-visibility: visible !important;
    -webkit-animation: iziT-flipOutX 0.7s cubic-bezier(0.4, 0.45, 0.15, 0.91) both;
    animation: iziT-flipOutX 0.7s cubic-bezier(0.4, 0.45, 0.15, 0.91) both;
}

.iziToast-overlay.fadeIn {
    -webkit-animation: iziT-fadeIn 0.5s ease both;
    animation: iziT-fadeIn 0.5s ease both;
}

.iziToast-overlay.fadeOut {
    -webkit-animation: iziT-fadeOut 0.7s ease both;
    animation: iziT-fadeOut 0.7s ease both;
}

@-webkit-keyframes iziT-revealIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(0.3, 0.3, 1);
    }
    100% {
        opacity: 1;
    }
}

@-moz-keyframes iziT-revealIn {
    0% {
        opacity: 0;
        -moz-transform: scale3d(0.3, 0.3, 1);
    }
    100% {
        opacity: 1;
    }
}

@-webkit-keyframes iziT-slideIn {
    0% {
        opacity: 0;
        -webkit-transform: translateX(50px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
    }
}

@-moz-keyframes iziT-slideIn {
    0% {
        opacity: 0;
        -moz-transform: translateX(50px);
    }
    100% {
        opacity: 1;
        -moz-transform: translateX(0);
    }
}

@-webkit-keyframes iziT-bounceInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(280px);
    }
    50% {
        opacity: 1;
        -webkit-transform: translateX(-20px);
    }
    70% {
        -webkit-transform: translateX(10px);
    }
    100% {
        -webkit-transform: translateX(0);
    }
}

@-webkit-keyframes iziT-bounceInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-280px);
    }
    50% {
        opacity: 1;
        -webkit-transform: translateX(20px);
    }
    70% {
        -webkit-transform: translateX(-10px);
    }
    100% {
        -webkit-transform: translateX(0);
    }
}

@-webkit-keyframes iziT-bounceInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-200px);
    }
    50% {
        opacity: 1;
        -webkit-transform: translateY(10px);
    }
    70% {
        -webkit-transform: translateY(-5px);
    }
    100% {
        -webkit-transform: translateY(0);
    }
}

@-webkit-keyframes iziT-bounceInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(200px);
    }
    50% {
        opacity: 1;
        -webkit-transform: translateY(-10px);
    }
    70% {
        -webkit-transform: translateY(5px);
    }
    100% {
        -webkit-transform: translateY(0);
    }
}

@-webkit-keyframes iziT-fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-webkit-keyframes iziT-fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(300px, 0, 0);
        transform: translate3d(300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-300px, 0, 0);
        transform: translate3d(-300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    }
    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
    }
    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    }
    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

@-webkit-keyframes iziT-fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@-webkit-keyframes iziT-fadeOutDown {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@-webkit-keyframes iziT-fadeOutUp {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@-webkit-keyframes iziT-fadeOutLeft {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0);
    }
}

@-webkit-keyframes iziT-fadeOutRight {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
    }
}

@-webkit-keyframes iziT-flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1;
    }
    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
}

@-moz-keyframes iziT-revealIn {
    0% {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 1);
    }
    100% {
        opacity: 1;
    }
}

@-webkit-keyframes iziT-revealIn {
    0% {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 1);
    }
    100% {
        opacity: 1;
    }
}

@-o-keyframes iziT-revealIn {
    0% {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 1);
    }
    100% {
        opacity: 1;
    }
}

@keyframes iziT-revealIn {
    0% {
        opacity: 0;
        transform: scale3d(0.3, 0.3, 1);
    }
    100% {
        opacity: 1;
    }
}

@-moz-keyframes iziT-slideIn {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@-webkit-keyframes iziT-slideIn {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@-o-keyframes iziT-slideIn {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes iziT-slideIn {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@-moz-keyframes iziT-bounceInLeft {
    0% {
        opacity: 0;
        transform: translateX(280px);
    }
    50% {
        opacity: 1;
        transform: translateX(-20px);
    }
    70% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0);
    }
}

@-webkit-keyframes iziT-bounceInLeft {
    0% {
        opacity: 0;
        transform: translateX(280px);
    }
    50% {
        opacity: 1;
        transform: translateX(-20px);
    }
    70% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0);
    }
}

@-o-keyframes iziT-bounceInLeft {
    0% {
        opacity: 0;
        transform: translateX(280px);
    }
    50% {
        opacity: 1;
        transform: translateX(-20px);
    }
    70% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0);
    }
}

@keyframes iziT-bounceInLeft {
    0% {
        opacity: 0;
        transform: translateX(280px);
    }
    50% {
        opacity: 1;
        transform: translateX(-20px);
    }
    70% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0);
    }
}

@-moz-keyframes iziT-bounceInRight {
    0% {
        opacity: 0;
        transform: translateX(-280px);
    }
    50% {
        opacity: 1;
        transform: translateX(20px);
    }
    70% {
        transform: translateX(-10px);
    }
    100% {
        transform: translateX(0);
    }
}

@-webkit-keyframes iziT-bounceInRight {
    0% {
        opacity: 0;
        transform: translateX(-280px);
    }
    50% {
        opacity: 1;
        transform: translateX(20px);
    }
    70% {
        transform: translateX(-10px);
    }
    100% {
        transform: translateX(0);
    }
}

@-o-keyframes iziT-bounceInRight {
    0% {
        opacity: 0;
        transform: translateX(-280px);
    }
    50% {
        opacity: 1;
        transform: translateX(20px);
    }
    70% {
        transform: translateX(-10px);
    }
    100% {
        transform: translateX(0);
    }
}

@keyframes iziT-bounceInRight {
    0% {
        opacity: 0;
        transform: translateX(-280px);
    }
    50% {
        opacity: 1;
        transform: translateX(20px);
    }
    70% {
        transform: translateX(-10px);
    }
    100% {
        transform: translateX(0);
    }
}

@-moz-keyframes iziT-bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(-200px);
    }
    50% {
        opacity: 1;
        transform: translateY(10px);
    }
    70% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

@-webkit-keyframes iziT-bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(-200px);
    }
    50% {
        opacity: 1;
        transform: translateY(10px);
    }
    70% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

@-o-keyframes iziT-bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(-200px);
    }
    50% {
        opacity: 1;
        transform: translateY(10px);
    }
    70% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes iziT-bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(-200px);
    }
    50% {
        opacity: 1;
        transform: translateY(10px);
    }
    70% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

@-moz-keyframes iziT-bounceInUp {
    0% {
        opacity: 0;
        transform: translateY(200px);
    }
    50% {
        opacity: 1;
        transform: translateY(-10px);
    }
    70% {
        transform: translateY(5px);
    }
    100% {
        transform: translateY(0);
    }
}

@-webkit-keyframes iziT-bounceInUp {
    0% {
        opacity: 0;
        transform: translateY(200px);
    }
    50% {
        opacity: 1;
        transform: translateY(-10px);
    }
    70% {
        transform: translateY(5px);
    }
    100% {
        transform: translateY(0);
    }
}

@-o-keyframes iziT-bounceInUp {
    0% {
        opacity: 0;
        transform: translateY(200px);
    }
    50% {
        opacity: 1;
        transform: translateY(-10px);
    }
    70% {
        transform: translateY(5px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes iziT-bounceInUp {
    0% {
        opacity: 0;
        transform: translateY(200px);
    }
    50% {
        opacity: 1;
        transform: translateY(-10px);
    }
    70% {
        transform: translateY(5px);
    }
    100% {
        transform: translateY(0);
    }
}

@-moz-keyframes iziT-fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-webkit-keyframes iziT-fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-o-keyframes iziT-fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes iziT-fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@-moz-keyframes iziT-fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-o-keyframes iziT-fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes iziT-fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-moz-keyframes iziT-fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-o-keyframes iziT-fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes iziT-fadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-moz-keyframes iziT-fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(300px, 0, 0);
        transform: translate3d(300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(300px, 0, 0);
        transform: translate3d(300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-o-keyframes iziT-fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(300px, 0, 0);
        transform: translate3d(300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes iziT-fadeInLeft {
    from {
        opacity: 0;
        -webkit-transform: translate3d(300px, 0, 0);
        transform: translate3d(300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-moz-keyframes iziT-fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-300px, 0, 0);
        transform: translate3d(-300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-webkit-keyframes iziT-fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-300px, 0, 0);
        transform: translate3d(-300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-o-keyframes iziT-fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-300px, 0, 0);
        transform: translate3d(-300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@keyframes iziT-fadeInRight {
    from {
        opacity: 0;
        -webkit-transform: translate3d(-300px, 0, 0);
        transform: translate3d(-300px, 0, 0);
    }
    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

@-moz-keyframes iziT-flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    }
    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
    }
    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    }
    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

@-webkit-keyframes iziT-flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    }
    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
    }
    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    }
    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

@-o-keyframes iziT-flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    }
    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
    }
    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    }
    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

@keyframes iziT-flipInX {
    from {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
    40% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    }
    60% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
        opacity: 1;
    }
    80% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    }
    to {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
}

@-moz-keyframes iziT-fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@-webkit-keyframes iziT-fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@-o-keyframes iziT-fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes iziT-fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@-moz-keyframes iziT-fadeOutDown {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@-webkit-keyframes iziT-fadeOutDown {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@-o-keyframes iziT-fadeOutDown {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@keyframes iziT-fadeOutDown {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@-moz-keyframes iziT-fadeOutUp {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@-webkit-keyframes iziT-fadeOutUp {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@-o-keyframes iziT-fadeOutUp {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@keyframes iziT-fadeOutUp {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@-moz-keyframes iziT-fadeOutLeft {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0);
    }
}

@-webkit-keyframes iziT-fadeOutLeft {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0);
    }
}

@-o-keyframes iziT-fadeOutLeft {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0);
    }
}

@keyframes iziT-fadeOutLeft {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0);
    }
}

@-moz-keyframes iziT-fadeOutRight {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
    }
}

@-webkit-keyframes iziT-fadeOutRight {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
    }
}

@-o-keyframes iziT-fadeOutRight {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
    }
}

@keyframes iziT-fadeOutRight {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
    }
}

@-moz-keyframes iziT-flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1;
    }
    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
}

@-webkit-keyframes iziT-flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1;
    }
    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
}

@-o-keyframes iziT-flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1;
    }
    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
}

@keyframes iziT-flipOutX {
    from {
        -webkit-transform: perspective(400px);
        transform: perspective(400px);
    }
    30% {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
        opacity: 1;
    }
    to {
        -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
        opacity: 0;
    }
}