/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.6.2 (2020-12-08)
 */
!function(){"use strict";var t,e,n,r,l=function(t){var e=t;return{get:function(){return e},set:function(t){e=t}}},o=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=tinymce.util.Tools.resolve("tinymce.util.Tools"),i=function(){},u=function(t){return function(){return t}},a=u(!1),c=u(!0),f=function(){return s},s=(t=function(t){return t.isNone()},{fold:function(t,e){return t()},is:a,isSome:a,isNone:c,getOr:n=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:u(null),getOrUndefined:u(undefined),or:n,orThunk:e,map:f,each:i,bind:f,exists:a,forall:c,filter:f,equals:t,equals_:t,toArray:function(){return[]},toString:u("none()")}),m=function(n){var t=u(n),e=function(){return o},r=function(t){return t(n)},o={fold:function(t,e){return e(n)},is:function(t){return n===t},isSome:c,isNone:a,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(t){return m(t(n))},each:function(t){t(n)},bind:r,exists:r,forall:r,filter:function(t){return t(n)?o:s},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(t){return t.is(n)},equals_:function(t,e){return t.fold(a,function(t){return e(n,t)})}};return o},g={some:m,none:f,from:function(t){return null===t||t===undefined?s:m(t)}},h=function(t){return!(null===(e=t)||e===undefined);var e},v=(r="function",function(t){return typeof t===r});function p(t,e){return b(document.createElement("canvas"),t,e)}function y(t){var e=p(t.width,t.height);return w(e).drawImage(t,0,0),e}function w(t){return t.getContext("2d")}function b(t,e,n){return t.width=e,t.height=n,t}var I,T,_,R,U=window.Promise?window.Promise:(I=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],C(t,A(E,this),A(L,this))},T=window,_=I.immediateFn||"function"==typeof T.setImmediate&&T.setImmediate||function(t){setTimeout(t,1)},R=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},I.prototype["catch"]=function(t){return this.then(null,t)},I.prototype.then=function(n,r){var o=this;return new I(function(t,e){x.call(o,new k(n,r,t,e))})},I.all=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var c=Array.prototype.slice.call(1===t.length&&R(t[0])?t[0]:t);return new I(function(o,i){if(0===c.length)return o([]);for(var u=c.length,t=0;t<c.length;t++)!function a(e,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void n.call(t,function(t){a(e,t)},i)}c[e]=t,0==--u&&o(c)}catch(r){i(r)}}(t,c[t])})},I.resolve=function(e){return e&&"object"==typeof e&&e.constructor===I?e:new I(function(t){t(e)})},I.reject=function(n){return new I(function(t,e){e(n)})},I.race=function(o){return new I(function(t,e){for(var n=0,r=o;n<r.length;n++)r[n].then(t,e)})},I);function A(t,e){return function(){return t.apply(e,arguments)}}function x(r){var o=this;null!==this._state?_(function(){var t,e=o._state?r.onFulfilled:r.onRejected;if(null!==e){try{t=e(o._value)}catch(n){return void r.reject(n)}r.resolve(t)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function E(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void C(A(e,t),A(E,this),A(L,this))}this._state=!0,this._value=t,j.call(this)}catch(n){L.call(this,n)}}function L(t){this._state=!1,this._value=t,j.call(this)}function j(){for(var t=0,e=this._deferreds;t<e.length;t++){var n=e[t];x.call(this,n)}this._deferreds=[]}function k(t,e,n,r){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.resolve=n,this.reject=r}function C(t,e,n){var r=!1;try{t(function(t){r||(r=!0,e(t))},function(t){r||(r=!0,n(t))})}catch(o){if(r)return;r=!0,n(o)}}function O(t){var r,e=t.src;return 0===e.indexOf("data:")?S(e):(r=e,new U(function(t,n){var e=new XMLHttpRequest;e.open("GET",r,!0),e.responseType="blob",e.onload=function(){200===this.status&&t(this.response)},e.onerror=function(){var t,e=this;n(0===this.status?((t=new Error("No access to download image")).code=18,t.name="SecurityError",t):new Error("Error "+e.status+" downloading image"))},e.send()}))}function P(a){return new U(function(t,e){var n=URL.createObjectURL(a),r=new Image,o=function(){r.removeEventListener("load",i),r.removeEventListener("error",u)};function i(){o(),t(r)}function u(){o(),e("Unable to load data of type "+a.type+": "+n)}r.addEventListener("load",i),r.addEventListener("error",u),r.src=n,r.complete&&i()})}function S(n){return new U(function(t,e){(function(t){var e=t.split(","),n=/data:([^;]+)/.exec(e[0]);if(!n)return g.none();for(var r=n[1],o=e[1],i=atob(o),u=i.length,a=Math.ceil(u/1024),c=new Array(a),f=0;f<a;++f){for(var s=1024*f,l=Math.min(1024+s,u),d=new Array(l-s),m=s,h=0;m<l;++h,++m)d[h]=i[m].charCodeAt(0);c[f]=new Uint8Array(d)}return g.some(new Blob(c,{type:r}))})(n).fold(function(){e("uri is not base64: "+n)},t)})}function M(t,r,o){return r=r||"image/png",v(HTMLCanvasElement.prototype.toBlob)?new U(function(e,n){t.toBlob(function(t){t?e(t):n()},r,o)}):S(t.toDataURL(r,o))}function B(t){return P(t).then(function(t){var e;e=t,URL.revokeObjectURL(e.src);var n,r,o=p((r=t).naturalWidth||r.width,(n=t).naturalHeight||n.height);return w(o).drawImage(t,0,0),o})}var N=P,D=O,F=function(t,e){return function(t,e,n){for(var r=0,o=t.length;r<o;r++){var i=t[r];if(e(i,r))return g.some(i);if(n(i,r))break}return g.none()}(t,e,a)};function H(t,e,n){var r=e.type;function o(r,o){return t.then(function(t){return n=o,e=(e=r)||"image/png",t.toDataURL(e,n);var e,n})}return{getType:u(r),toBlob:function(){return U.resolve(e)},toDataURL:u(n),toBase64:function(){return n.split(",")[1]},toAdjustedBlob:function(e,n){return t.then(function(t){return M(t,e,n)})},toAdjustedDataURL:o,toAdjustedBase64:function(t,e){return o(t,e).then(function(t){return t.split(",")[1]})},toCanvas:function(){return t.then(y)}}}function q(e){return n=e,new U(function(t){var e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsDataURL(n)}).then(function(t){return H(B(e),e,t)});var n}function z(e,t){return M(e,t).then(function(t){return H(U.resolve(e),t,e.toDataURL())})}function $(e,n){return e.toCanvas().then(function(t){return function(t,e,n){var r=p(t.width,t.height),o=w(r),i=0,u=0;90!==(n=n<0?360+n:n)&&270!==n||b(r,r.height,r.width);90!==n&&180!==n||(i=r.width);270!==n&&180!==n||(u=r.height);return o.translate(i,u),o.rotate(n*Math.PI/180),o.drawImage(t,0,0),z(r,e)}(t,e.getType(),n)})}function G(e,n){return e.toCanvas().then(function(t){return function(t,e,n){var r=p(t.width,t.height),o=w(r);"v"===n?(o.scale(1,-1),o.drawImage(t,0,-r.height)):(o.scale(-1,1),o.drawImage(t,-r.width,0));return z(r,e)}(t,e.getType(),n)})}var J=G,K=$,V=Object.keys,W=function(e,r,o){return void 0===o&&(o=!1),new U(function(t){var n=new XMLHttpRequest;n.onreadystatechange=function(){4===n.readyState&&t({status:n.status,blob:n.response})},n.open("GET",e,!0),n.withCredentials=o,function(t,e){for(var n=V(t),r=0,o=n.length;r<o;r++){var i=n[r];e(t[i],i)}}(r,function(t,e){n.setRequestHeader(e,t)}),n.responseType="blob",n.send()})},X=[{code:404,message:"Could not find Image Proxy"},{code:403,message:"Rejected request"},{code:0,message:"Incorrect Image Proxy URL"}],Q=[{type:"not_found",message:"Failed to load image."},{type:"key_missing",message:"The request did not include an api key."},{type:"key_not_found",message:"The provided api key could not be found."},{type:"domain_not_trusted",message:"The api key is not valid for the request origins."}],Y=function(t,e){var n,r,o=(n=function(t,e){return h(t)?t[e]:undefined},r=t,function(t,e){for(var n=0,r=t.length;n<r;n++)e(t[n],n)}(e,function(t){r=n(r,t)}),r);return g.from(o)},Z=function(t){var e,n=(e=t,"ImageProxy HTTP error: "+F(X,function(t){return e===t.code}).fold(u("Unknown ImageProxy error"),function(t){return t.message}));return U.reject(n)},tt=function(e){return F(Q,function(t){return t.type===e}).fold(u("Unknown service error"),function(t){return t.message})},et=function(t){return"ImageProxy Service error: "+function(t){try{return g.some(JSON.parse(t))}catch(e){return g.none()}}(t).bind(function(t){return Y(t,["error","type"]).map(tt)}).getOr("Invalid JSON in service error message")},nt=function(t){return r=t,new U(function(t,e){var n=new FileReader;n.onload=function(){t(n.result)},n.onerror=function(t){e(t)},n.readAsText(r)}).then(function(t){var e=et(t);return U.reject(e)});var r},rt=function(t){return t<200||300<=t},ot=function(t,e){var n,r,o,i={"Content-Type":"application/json;charset=UTF-8","tiny-api-key":e};return W((r=e,o=-1===(n=t).indexOf("?")?"?":"&",/[?&]apiKey=/.test(n)?n:n+o+"apiKey="+encodeURIComponent(r)),i).then(function(t){return rt(t.status)?(e=t.status,n=t.blob,r=e,"application/json"!==(null==(o=n)?void 0:o.type)||400!==r&&403!==r&&404!==r&&500!==r?Z(e):nt(n)):U.resolve(t.blob);var e,n,r,o})},it=function(t,e,n){return void 0===n&&(n=!1),e?ot(t,e):W(t,{},n).then(function(t){return rt(t.status)?Z(t.status):U.resolve(t.blob)})},ut=q,at=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:t}},ct={fromHtml:function(t,e){var n=(e||document).createElement("div");if(n.innerHTML=t,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return at(n.childNodes[0])},fromTag:function(t,e){var n=(e||document).createElement(t);return at(n)},fromText:function(t,e){var n=(e||document).createTextNode(t);return at(n)},fromDom:at,fromPoint:function(t,e,n){return g.from(t.dom.elementFromPoint(e,n)).map(at)}},ft=("undefined"!=typeof window||Function("return this;")(),function(t,e){return n=function(t){return function(t,e){var n=t.dom;if(1!==n.nodeType)return!1;var r=n;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}(t,e)},F(t.dom.childNodes,function(t){return n(ct.fromDom(t))}).map(ct.fromDom);var n}),st=tinymce.util.Tools.resolve("tinymce.util.Delay"),lt=tinymce.util.Tools.resolve("tinymce.util.Promise"),dt=tinymce.util.Tools.resolve("tinymce.util.URI"),mt=function(t){return t.getParam("imagetools_proxy")};function ht(t){var e,n;function r(t){return/^[0-9\.]+px$/.test(t)}return e=t.style.width,n=t.style.height,e||n?r(e)&&r(n)?{w:parseInt(e,10),h:parseInt(n,10)}:null:(e=t.width,n=t.height,e&&n?{w:parseInt(e,10),h:parseInt(n,10)}:null)}function gt(t){return{w:t.naturalWidth,h:t.naturalHeight}}var vt=0,pt=function(t){return ft(ct.fromDom(t),"img")},yt=function(t,e){return t.dom.is(e,"figure")},wt=function(t,e){return t.dom.is(e,"img:not([data-mce-object],[data-mce-placeholder])")},bt=function(e,t){var n=function(t){return wt(e,t)&&(Rt(e,t)||Ut(e,t)||h(mt(e)))};return yt(e,t)?pt(t).bind(function(t){return n(t.dom)?g.some(t.dom):g.none()}):n(t)?g.some(t):g.none()},It=function(t,e){t.notificationManager.open({text:e,type:"error"})},Tt=function(t){var e=t.selection.getNode(),n=t.dom.getParent(e,"figure.image");return null!==n&&yt(t,n)?pt(n):wt(t,e)?g.some(ct.fromDom(e)):g.none()},_t=function(t,e,n){var r=e.match(/(?:\/|^)(([^\/\?]+)\.(?:[a-z0-9.]+))(?:\?|$)/i);return h(r)?t.dom.encode(r[n]):null},Rt=function(t,e){var n=e.src;return 0===n.indexOf("data:")||0===n.indexOf("blob:")||new dt(n).host===t.documentBaseURI.host},Ut=function(t,e){return-1!==d.inArray(t.getParam("imagetools_cors_hosts",[],"string[]"),new dt(e.src).host)},At=function(t,e){if(Ut(t,e))return it(e.src,null,(n=t,r=e,-1!==d.inArray(n.getParam("imagetools_credentials_hosts",[],"string[]"),new dt(r.src).host)));var n,r,o;if(Rt(t,e))return D(e);var i=mt(t),u=i+(-1===i.indexOf("?")?"?":"&")+"url="+encodeURIComponent(e.src),a=(o=t).getParam("api_key",o.getParam("imagetools_api_key","","string"),"string");return it(u,a,!1)},xt=function(t,e){return n=t,g.from(n.getParam("imagetools_fetch_image",null,"function")).fold(function(){return At(t,e)},function(t){return t(e)});var n},Et=function(t,e){var n=t.editorUpload.blobCache.getByUri(e.src);return n?lt.resolve(n.blob()):xt(t,e)},Lt=function(t){st.clearTimeout(t.get())},jt=function(a,c,f,s,l,d,m){return f.toBlob().then(function(t){var e,n,o,r=a.editorUpload.blobCache,i=d.src,u=c.type===t.type;return a.getParam("images_reuse_filename",!1,"boolean")&&(o=r.getByUri(i),n=h(o)?(i=o.uri(),e=o.name(),o.filename()):(e=_t(a,i,2),_t(a,i,1))),o=r.create({id:"imagetools"+vt++,blob:t,base64:f.toBase64(),uri:i,name:e,filename:u?n:undefined}),r.add(o),a.undoManager.transact(function(){a.$(d).on("load",function r(){var t,e,n;a.$(d).off("load",r),a.nodeChanged(),s?a.editorUpload.uploadImagesAuto():(Lt(l),t=a,e=l,n=st.setEditorTimeout(t,function(){t.editorUpload.uploadImagesAuto()},t.getParam("images_upload_timeout",3e4,"number")),e.set(n))}),m&&a.$(d).attr({width:m.w,height:m.h}),a.$(d).attr({src:o.blobUri()}).removeAttr("data-mce-src")}),o})},kt=function(r,o,t,i){return function(){return Tt(r).fold(function(){It(r,"Could not find selected image")},function(n){return r._scanForImages().then(function(){return Et(r,n.dom)}).then(function(e){return ut(e).then(t).then(function(t){return jt(r,e,t,!1,o,n.dom,i)})})["catch"](function(t){It(r,t)})})}},Ct=function(e,n,r){return function(){var t=Tt(e).fold(function(){return null},function(t){var e=ht(t.dom);return e?{w:e.h,h:e.w}:null});return kt(e,n,function(t){return K(t,r)},t)()}},Ot=function(t,e,n){return function(){return kt(t,e,function(t){return J(t,n)})()}},Pt=function(e,n,u,a,c){return N(c).then(function(t){var e,n,r,o,i=gt(t);return a.w===i.w&&a.h===i.h||ht(u)&&(e=u,(n=i)&&(r=e.style.width,o=e.style.height,(r||o)&&(e.style.width=n.w+"px",e.style.height=n.h+"px",e.removeAttribute("data-mce-style")),r=e.width,o=e.height,(r||o)&&(e.setAttribute("width",String(n.w)),e.setAttribute("height",String(n.h))))),URL.revokeObjectURL(t.src),c}).then(ut).then(function(t){return jt(e,c,t,!0,n,u)})["catch"](function(){})},St=function(i,u){return function(){var r=Tt(i),o=r.map(function(t){return gt(t.dom)});r.each(function(e){bt(i,e.dom).each(function(t){Et(i,e.dom).then(function(t){var e,n={blob:e=t,url:URL.createObjectURL(e)};i.windowManager.open({title:"Edit Image",size:"large",body:{type:"panel",items:[{type:"imagetools",name:"imagetools",label:"Edit Image",currentState:n}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0,disabled:!0}],onSubmit:function(t){var n=t.getData().imagetools.blob;r.each(function(e){o.each(function(t){Pt(i,u,e.dom,t,n)})}),t.close()},onCancel:function(){},onAction:function(t,e){switch(e.name){case"save-state":e.value?t.enable("save"):t.disable("save");break;case"disable":t.disable("save"),t.disable("cancel");break;case"enable":t.enable("cancel")}}})})})})}};o.add("imagetools",function(t){var n,e,r,o,i,u,a,c,f=l(0),s=l(null);n=t,e=f,d.each({mceImageRotateLeft:Ct(n,e,-90),mceImageRotateRight:Ct(n,e,90),mceImageFlipVertical:Ot(n,e,"v"),mceImageFlipHorizontal:Ot(n,e,"h"),mceEditImage:St(n,e)},function(t,e){n.addCommand(e,t)}),o=function(t){return function(){return r.execCommand(t)}},(r=t).ui.registry.addButton("rotateleft",{tooltip:"Rotate counterclockwise",icon:"rotate-left",onAction:o("mceImageRotateLeft")}),r.ui.registry.addButton("rotateright",{tooltip:"Rotate clockwise",icon:"rotate-right",onAction:o("mceImageRotateRight")}),r.ui.registry.addButton("flipv",{tooltip:"Flip vertically",icon:"flip-vertically",onAction:o("mceImageFlipVertical")}),r.ui.registry.addButton("fliph",{tooltip:"Flip horizontally",icon:"flip-horizontally",onAction:o("mceImageFlipHorizontal")}),r.ui.registry.addButton("editimage",{tooltip:"Edit image",icon:"edit-image",onAction:o("mceEditImage"),onSetup:function(e){var t=function(){var t=Tt(r).forall(function(t){return bt(r,t.dom).isNone()});e.setDisabled(t)};return r.on("NodeChange",t),function(){r.off("NodeChange",t)}}}),r.ui.registry.addButton("imageoptions",{tooltip:"Image options",icon:"image",onAction:o("mceImage")}),r.ui.registry.addContextMenu("imagetools",{update:function(t){return bt(r,t).fold(function(){return[]},function(t){return[{text:"Edit image",icon:"edit-image",onAction:o("mceEditImage")}]})}}),(i=t).ui.registry.addContextToolbar("imagetools",{items:i.getParam("imagetools_toolbar","rotateleft rotateright flipv fliph editimage imageoptions"),predicate:function(t){return bt(i,t).isSome()},position:"node",scope:"node"}),a=f,c=s,(u=t).on("NodeChange",function(t){var e=c.get(),n=bt(u,t.element);e&&!n.exists(function(t){return e.src===t.src})&&(Lt(a),u.editorUpload.uploadImagesAuto(),c.set(null)),n.each(c.set)})})}();