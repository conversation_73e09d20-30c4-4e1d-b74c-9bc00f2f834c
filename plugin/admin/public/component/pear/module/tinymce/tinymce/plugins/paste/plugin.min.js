/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.6.2 (2020-12-08)
 */
!function(){"use strict";var e,t,n,r,d=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},a=tinymce.util.Tools.resolve("tinymce.PluginManager"),o=function(e){return function(){return e}},i=o(!1),s=o(!0),u=function(){return l},l=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:i,isSome:i,isNone:s,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:o(null),getOrUndefined:o(undefined),or:n,orThunk:t,map:u,each:function(){},bind:u,exists:i,forall:s,filter:u,equals:e,equals_:e,toArray:function(){return[]},toString:o("none()")}),c=function(n){var e=o(n),t=function(){return a},r=function(e){return e(n)},a={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:s,isNone:i,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return c(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?a:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(i,function(e){return t(n,e)})}};return a},f={some:c,none:u,from:function(e){return null===e||e===undefined?l:c(e)}},P=function(e){return!(null===(t=e)||t===undefined);var t},m=(r="function",function(e){return typeof e===r}),p=Array.prototype.slice,g=function(e,t){for(var n=0,r=e.length;n<r;n++){if(t(e[n],n))return!0}return!1},v=function(e,t){for(var n=e.length,r=new Array(n),a=0;a<n;a++){var o=e[a];r[a]=t(o,a)}return r},h=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},y=m(Array.from)?Array.from:function(e){return p.call(e)},b=function(){var t=d(f.none());return{clear:function(){return t.set(f.none())},set:function(e){return t.set(f.some(e))},isSet:function(){return t.get().isSome()},on:function(e){return t.get().each(e)}}},x=function(e,t,n){return""===t||e.length>=t.length&&e.substr(n,n+t.length)===t},w=tinymce.util.Tools.resolve("tinymce.Env"),_=tinymce.util.Tools.resolve("tinymce.util.Delay"),T=tinymce.util.Tools.resolve("tinymce.util.Promise"),C=tinymce.util.Tools.resolve("tinymce.util.VK"),D=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},k=tinymce.util.Tools.resolve("tinymce.util.Tools"),S=function(e){return e.getParam("paste_data_images",!1)},O=function(e){return e.getParam("paste_retain_style_properties")},j=function(e){return e.getParam("validate")},R=function(e){return e.getParam("paste_data_images",!1,"boolean")},A=function(e){return k.explode(e.getParam("images_file_types","jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp","string"))},I="x-tinymce/html",F="\x3c!-- "+I+" --\x3e",E=function(e){return-1!==e.indexOf(F)},M=tinymce.util.Tools.resolve("tinymce.html.Entities"),N=function(e,t,n){var r=e.split(/\n\n/),a=function(e,t){var n,r=[],a="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+M.encodeAllRaw(t[n])+'"');r.length&&(a+=" "+r.join(" "))}return a+">"}(t,n),o="</"+t+">",i=k.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===i.length?i[0]:k.map(i,function(e){return a+e+o}).join("")},B=tinymce.util.Tools.resolve("tinymce.html.DomParser"),H=tinymce.util.Tools.resolve("tinymce.html.Serializer"),L="\xa0",$=tinymce.util.Tools.resolve("tinymce.html.Node"),z=tinymce.util.Tools.resolve("tinymce.html.Schema");function U(t,e){return k.each(e,function(e){t=e.constructor===RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function q(e){var t=z(),n=B({},t),r="",a=t.getShortEndedElements(),o=k.makeMap("script noscript style textarea video audio iframe object"," "),i=t.getBlockElements();return e=U(e,[/<!\[[^\]]+\]>/g]),function s(e){var t=e.name,n=e;if("br"!==t){if("wbr"!==t)if(a[t]&&(r+=" "),o[t])r+=" ";else{if(3===e.type&&(r+=e.value),!e.shortEnded&&(e=e.firstChild))for(;s(e),e=e.next;);i[t]&&n.next&&(r+="\n","p"===t&&(r+="\n"))}}else r+="\n"}(n.parse(e)),r}function V(e){return e=U(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function(e,t,n){return t||n?L:" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])}function K(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function X(e){var o,i,s=1;function u(e,t){if(3!==e.type||!t.test(e.value)){if(e=e.firstChild)do{if(!u(e,t))return}while(e=e.next);return 1}e.value=e.value.replace(t,"")}function t(e,t,n){var r=e._listLevel||s;r!==s&&(o=r<s?o&&o.parent.parent:(i=o,null)),o&&o.name===t?o.append(e):(i=i||o,o=new $(t,1),1<n&&o.attr("start",""+n),e.wrap(o)),e.name="li",s<r&&i&&i.lastChild.append(o),s=r,function a(e){if(e._listIgnore)e.remove();else if(e=e.firstChild)for(;a(e),e=e.next;);}(e),u(e,/^\u00a0+/),u(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),u(e,/^\u00a0+/)}for(var n=[],r=e.firstChild;null!=r;)if(n.push(r),null!==(r=r.walk()))for(;void 0!==r&&r.parent!==e;)r=r.walk();for(var a=0;a<n.length;a++)if("p"===(e=n[a]).name&&e.firstChild){var l=function d(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=d(e),e=e.next;);return t}(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(l)){t(e,"ul");continue}if(function(t){var n;return t=t.replace(/^[\u00a0 ]+/,""),k.each([/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],function(e){if(e.test(t))return!(n=!0)}),n}(l)){var c=/([0-9]+)\./.exec(l),f=1;c&&(f=parseInt(c[1],10)),t(e,"ol",f);continue}if(e._listLevel){t(e,"ul",1);continue}o=null}else i=o,o=null}var W,Y,Z=function(r,e){var a,t=O(r);t&&(a=k.makeMap(t.split(/[, ]/))),e=U(e,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,L],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return 0<t.length?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join(L):""}]]);var n=r.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody"),o=z({valid_elements:n,valid_children:"-li[p]"});k.each(o.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var i=B({},o);i.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)(t=e[n]).attr("style",function(n,r,a,o){var i,s={},e=n.dom.parseStyle(o);return k.each(e,function(e,t){switch(t){case"mso-list":(i=/\w+ \w+([0-9]+)/i.exec(o))&&(a._listLevel=parseInt(i[1],10)),/Ignore/i.test(e)&&a.firstChild&&(a._listIgnore=!0,a.firstChild._listIgnore=!0);break;case"horiz-align":t="text-align";break;case"vert-align":t="vertical-align";break;case"font-color":case"mso-foreground":t="color";break;case"mso-background":case"mso-highlight":t="background";break;case"font-weight":case"font-style":return void("normal"!==e&&(s[t]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void a.remove()}0!==t.indexOf("mso-comment")?0!==t.indexOf("mso-")&&("all"===O(n)||r&&r[t])&&(s[t]=e):a.remove()}),/(bold)/i.test(s["font-weight"])&&(delete s["font-weight"],a.wrap(new $("b",1))),/(italic)/i.test(s["font-style"])&&(delete s["font-style"],a.wrap(new $("i",1))),(s=n.dom.serializeStyle(s,a.name))||null}(r,a,t,t.attr("style"))),"span"===t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),i.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),i.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),i.addNodeFilter("a",function(e){for(var t,n,r,a=e.length;a--;)if(n=(t=e[a]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=(n=n.split("#")[1])&&"#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=i.parse(e);return r.getParam("paste_convert_word_fake_lists",!0)&&X(s),e=H({validate:j(r)},o).serialize(s)},G=function(e,t){return{content:e,cancelled:t}},J=function(e,t,n,r){var a,o,i,s,u,l,c,f,d,m,p,g,v=(a=t,o=n,i=r,e.fire("PastePreProcess",{content:a,internal:o,wordContent:i})),h=function(e,t){var n=B({},e.schema);n.addNodeFilter("meta",function(e){k.each(e,function(e){e.remove()})});var r=n.parse(t,{forced_root_block:!1,isRootContent:!0});return H({validate:j(e)},e.schema).serialize(r)}(e,v.content);return e.hasEventListeners("PastePostProcess")&&!v.isDefaultPrevented()?(u=h,l=n,c=r,p=(s=e).dom.create("div",{style:"display:none"},u),f=p,d=l,m=c,g=s.fire("PastePostProcess",{node:f,internal:d,wordContent:m}),G(g.node.innerHTML,g.isDefaultPrevented())):G(h,v.isDefaultPrevented())},Q=function(e,t,n){var r,a,o=K(t),i=o?(a=t,(r=e).getParam("paste_enable_default_filters",!0)?Z(r,a):a):t;return J(e,i,n,o)},ee=function(e,t){return e.insertContent(t,{merge:e.getParam("paste_merge_formats",!0),paste:!0}),!0},te=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},ne=function(e,r){return te(r)&&g(A(e),function(e){return x(t=r,n="."+e,t.length-n.length);var t,n})},re=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!te(t))&&(a=t,o=n,(r=e).undoManager.extra(function(){o(r,a)},function(){r.execCommand("mceInsertLink",!1,a)}),!0);var r,a,o},ae=function(e,t,n){return!!ne(e,t)&&(a=t,o=n,(r=e).undoManager.extra(function(){o(r,a)},function(){r.insertContent('<img src="'+a+'">')}),!0);var r,a,o},oe=function(e,t,n){var r,a;n||!1===e.getParam("smart_paste",!0)?ee(e,t):(r=e,a=t,k.each([re,ae,ee],function(e){return!0!==e(r,a,ee)}))},ie=function(e){return"\n"===e||"\r"===e},se=function(e,t){var n,r,a,o,i=(n=" ",(r=e.getParam("paste_tab_spaces",4,"number"))<=0?"":new Array(r+1).join(n)),s=t.replace(/\t/g,i);return(o={pcIsSpace:!(a=function(e,t){return-1!==" \f\t\x0B".indexOf(t)||t===L?e.pcIsSpace||""===e.str||e.str.length===s.length-1||(n=s,(r=e.str.length+1)<n.length&&0<=r&&ie(n[r]))?{pcIsSpace:!1,str:e.str+L}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:ie(t),str:e.str+t};var n,r}),str:""},h(s,function(e){o=a(o,e)}),o).str},ue=function(e,t,n,r){var a=Q(e,t,n);!1===a.cancelled&&oe(e,a.content,r)},le=function(e,t,n){var r=n||E(t);ue(e,t.replace(F,""),r,!1)},ce=function(e,t){var n,r,a,o=e.dom.encode(t).replace(/\r\n/g,"\n"),i=se(e,o),s=(n=i,r=e.getParam("forced_root_block"),a=e.getParam("forced_root_block_attrs"),r?N(n,!0===r?"p":r,a):n.replace(/\r?\n/g,"<br>"));ue(e,s,!1,!0)},fe=function(e){var t,n={};if(e&&(!e.getData||(t=e.getData("Text"))&&0<t.length&&-1===t.indexOf("data:text/mce-internal,")&&(n["text/plain"]=t),e.types))for(var r=0;r<e.types.length;r++){var a=e.types[r];try{n[a]=e.getData(a)}catch(o){n[a]=""}}return n},de=function(e,t){return t in e&&0<e[t].length},me=function(e){return de(e,"text/html")||de(e,"text/plain")},pe=(W="mceclip",Y=0,function(){return W+Y++}),ge=function(e,t){var n,r,a,o,i,s,u,l,c,f,d,m,p,g=(n=t.uri,(r=/data:([^;]+);base64,([a-z0-9\+\/=]+)/i.exec(n))?{type:r[1],data:decodeURIComponent(r[2])}:{type:null,data:null}),v=g.data,h=g.type,y=pe(),b=t.blob,x=new Image;x.src=t.uri,m=x,!(p=e.getParam("images_dataimg_filter"))||p(m)?(l=void 0,(o=(a=e.editorUpload.blobCache).getByData(v,h))?l=o:(s=(i=e.getParam("images_reuse_filename")&&P(b.name))?(c=e,f=b.name,d=f.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i),P(d)?c.dom.encode(d[1]):null):y,u=i?b.name:undefined,l=a.create(y,b,v,s,u),a.add(l)),le(e,'<img src="'+l.blobUri()+'">',!1)):le(e,'<img src="'+t.uri+'">',!1)},ve=function(e){return T.all(v(e,function(r){return new T(function(e){var t=P(r.getAsFile)?r.getAsFile():r,n=new window.FileReader;n.onload=function(){e({blob:t,uri:n.result})},n.readAsDataURL(t)})}))},he=function(e){var t=A(e);return function(r){return e=r.type,x(e,"image/",0)&&g(t,function(e){return t=e,n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"},(k.hasOwn(n,t)?"image/"+n[t]:"image/"+t)===r.type;var t,n});var e}},ye=function(t,e,n){var r,a,o,i,s="paste"===e.type?e.clipboardData:e.dataTransfer;if(R(t)&&s){var u=(r=t,o=(a=s).items?v(y(a.items),function(e){return e.getAsFile()}):[],i=a.files?y(a.files):[],function(e,t){for(var n=[],r=0,a=e.length;r<a;r++){var o=e[r];t(o,r)&&n.push(o)}return n}(0<o.length?o:i,he(r)));if(0<u.length)return e.preventDefault(),ve(u).then(function(e){n&&t.selection.setRng(n),h(e,function(e){ge(t,e)})}),!0}return!1},be=function(e){return C.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},xe=function(s,u,l){var c,f=b(),d=b();function m(e,t,n,r,a){var o;de(t,"text/html")?o=t["text/html"]:(o=u.getHtml(),a=a||E(o),u.isDefaultContent(o)&&(r=!0)),o=V(o),u.remove();var i=!1===a&&!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(o),s=ne(e,o);o.length&&(!i||s)||(r=!0),(r||s)&&(o=de(t,"text/plain")&&i?t["text/plain"]:q(o)),u.isDefaultContent(o)?n||e.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):r?ce(e,o):le(e,o,a)}s.on("keyup",d.clear),s.on("keydown",function(e){var t,n=function(e){be(e)&&!e.isDefaultPrevented()&&u.remove()};if(be(e)&&!e.isDefaultPrevented()){if((c=e.shiftKey&&86===e.keyCode)&&w.webkit&&-1!==navigator.userAgent.indexOf("Version/"))return;if(e.stopImmediatePropagation(),f.set(e),d.set(!0),w.ie&&c)return e.preventDefault(),t=!0,void s.fire("paste",{ieFake:t});u.remove(),u.create(),s.once("keyup",n),s.once("paste",function(){s.off("keyup",n)})}});s.on("paste",function(e){var t=f.isSet()||d.isSet();t&&f.clear();var n,r,a=(n=s,fe(e.clipboardData||n.getDoc().dataTransfer)),o="text"===l.get()||c,i=de(a,I);c=!1,!(e.isDefaultPrevented()||(r=e.clipboardData,-1!==navigator.userAgent.indexOf("Android")&&r&&r.items&&0===r.items.length))&&(me(a)||!ye(s,e,u.getLastRng()||s.selection.getRng()))?(t||e.preventDefault(),!w.ie||t&&!e.ieFake||de(a,"text/html")||(u.create(),s.dom.bind(u.getEl(),"paste",function(e){e.stopPropagation()}),s.getDoc().execCommand("Paste",!1,null),a["text/html"]=u.getHtml()),de(a,"text/html")?(e.preventDefault(),i=i||E(a["text/html"]),m(s,a,t,o,i)):_.setEditorTimeout(s,function(){m(s,a,t,o,i)},0)):u.remove()})},Pe=function(i,e,t){var s;xe(i,e,t),i.parser.addNodeFilter("img",function(e,t,n){var r,a=function(e){e.attr("data-mce-object")||s===w.transparentSrc||e.remove()};if(!R(i)&&((r=n).data&&!0===r.data.paste))for(var o=e.length;o--;)(s=e[o].attr("src"))&&(0!==s.indexOf("webkit-fake-url")&&(i.getParam("allow_html_data_urls",!1,"boolean")||0!==s.indexOf("data:"))||a(e[o]))})},we=function(e){return w.ie&&e.inline?document.body:e.getBody()},_e=function(t,e,n){var r;we(r=t)!==r.getBody()&&t.dom.bind(e,"paste keyup",function(e){De(t,n)||t.fire("paste")})},Te=function(e){return e.dom.get("mcepastebin")},Ce=function(e,t){return t===e},De=function(e,t){var n,r=Te(e);return(n=r)&&"mcepastebin"===n.id&&Ce(t,r.innerHTML)},ke=function(e){var t=d(null),n="%MCEPASTEBIN%";return{create:function(){return function(e,t,n){var r=e.dom,a=e.getBody();t.set(e.selection.getRng());var o=e.dom.add(we(e),"div",{id:"mcepastebin","class":"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n);(w.ie||w.gecko)&&r.setStyle(o,"left","rtl"===r.getStyle(a,"direction",!0)?65535:-65535),r.bind(o,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),_e(e,o,n),o.focus(),e.selection.select(o,!0)}(e,t,n)},remove:function(){return function(e,t){if(Te(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(e,t)},getEl:function(){return Te(e)},getHtml:function(){return function(n){var t=function(e,t){e.appendChild(t),n.dom.remove(t,!0)},e=k.grep(we(n).childNodes,function(e){return"mcepastebin"===e.id}),r=e.shift();k.each(e,function(e){t(r,e)});for(var a=n.dom.select("div[id=mcepastebin]",r),o=a.length-1;0<=o;o--){var i=n.dom.create("div");r.insertBefore(i,a[o]),t(i,a[o])}return r?r.innerHTML:""}(e)},getLastRng:function(){return t.get()},isDefault:function(){return De(e,n)},isDefaultContent:function(e){return e===n}}},Se=function(n,r){n.addCommand("mceTogglePlainTextPaste",function(){var e,t;e=n,"text"===(t=r).pasteFormat.get()?(t.pasteFormat.set("html"),D(e,!1)):(t.pasteFormat.set("text"),D(e,!0)),e.focus()}),n.addCommand("mceInsertClipboardContent",function(e,t){t.content&&r.pasteHtml(t.content,t.internal),t.text&&r.pasteText(t.text)})},Oe=function(e,t,n){if(r=e,!1!==w.iOS||"function"!=typeof(null==r?void 0:r.setData))return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(I,t),!0}catch(a){return!1}var r},je=function(e,t,n,r){Oe(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},Re=function(s){return function(e,t){var n=F+e,r=s.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=s.dom.create("div",{contenteditable:"true"},n);s.dom.setStyles(r,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),r.appendChild(a),s.dom.add(s.getBody(),r);var o=s.selection.getRng();a.focus();var i=s.dom.createRng();i.selectNodeContents(a),s.selection.setRng(i),_.setTimeout(function(){s.selection.setRng(o),r.parentNode.removeChild(r),t()},0)}},Ae=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},Ie=function(e){return!e.selection.isCollapsed()||!!(t=e).dom.getParent(t.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",t.getBody());var t},Fe=function(e){var t,n;e.on("cut",(t=e,function(e){Ie(t)&&je(e,Ae(t),Re(t),function(){var e;w.browser.isChrome()||w.browser.isFirefox()?(e=t.selection.getRng(),_.setEditorTimeout(t,function(){t.selection.setRng(e),t.execCommand("Delete")},0)):t.execCommand("Delete")})})),e.on("copy",(n=e,function(e){Ie(n)&&je(e,Ae(n),Re(n),function(){})}))},Ee=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Me=function(e,t){return Ee.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},Ne=function(e,t){e.focus(),e.selection.setRng(t)},Be=function(i,s,u){i.getParam("paste_block_drop",!1)&&i.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),S(i)||i.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&0<t.files.length&&e.preventDefault()}),i.on("drop",function(e){var t,n,r,a,o=Me(i,e);e.isDefaultPrevented()||u.get()||(t=s.getDataTransferItems(e.dataTransfer),n=s.hasContentType(t,I),(!s.hasHtmlOrText(t)||(r=t["text/plain"])&&0===r.indexOf("file://"))&&s.pasteImageData(e,o)||!o||!i.getParam("paste_filter_drop",!0)||(a=t["mce-internal"]||t["text/html"]||t["text/plain"])&&(e.preventDefault(),_.setEditorTimeout(i,function(){i.undoManager.transact(function(){t["mce-internal"]&&i.execCommand("Delete"),Ne(i,o),a=V(a),t["text/html"]?s.pasteHtml(a,n):s.pasteText(a)})})))}),i.on("dragstart",function(e){u.set(!0)}),i.on("dragover dragend",function(e){S(i)&&!1===u.get()&&(e.preventDefault(),Ne(i,Me(i,e))),"dragend"===e.type&&u.set(!1)})};function He(t,n){t.on("PastePreProcess",function(e){e.content=n(t,e.content,e.internal,e.wordContent)})}function Le(e,t){if(!K(t))return t;var n=[];return k.each(e.schema.getBlockElements(),function(e,t){n.push(t)}),t=U(t,[[new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g"),"$1"]]),t=U(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function $e(e,t,n,r){if(r||n)return t;var l,c,f,a=e.getParam("paste_webkit_styles");return!1===e.getParam("paste_remove_styles_if_webkit",!0)||"all"===a?t:(a&&(l=a.split(/[, ]/)),t=(t=l?(c=e.dom,f=e.selection.getNode(),t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var a=c.parseStyle(c.decode(n)),o={};if("none"===l)return t+r;for(var i=0;i<l.length;i++){var s=a[l[i]],u=c.getStyle(f,l[i],!0);/color/.test(l[i])&&(s=c.toHex(s),u=c.toHex(u)),u!==s&&(o[l[i]]=s)}return(o=c.serializeStyle(o,"span"))?t+' style="'+o+'"'+r:t+r})):t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3")).replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r}))}function ze(n,e){n.$("a",e).find("font,u").each(function(e,t){n.dom.remove(t,!0)})}var Ue=function(n,r){return function(t){t.setActive("text"===r.pasteFormat.get());var e=function(e){return t.setActive(e.state)};return n.on("PastePlainTextToggle",e),function(){return n.off("PastePlainTextToggle",e)}}};a.add("paste",function(e){if(!1==(!!e.hasPlugin("powerpaste",!0)&&("undefined"!=typeof window.console&&window.console.log&&window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),!0))){var t=d(!1),n=d(e.getParam("paste_as_text",!1)?"text":"html"),r=(c=n,f=ke(l=e),l.on("PreInit",function(){return Pe(l,f,c)}),{pasteFormat:c,pasteHtml:function(e,t){return le(l,e,t)},pasteText:function(e){return ce(l,e)},pasteImageData:function(e,t){return ye(l,e,t)},getDataTransferItems:fe,hasHtmlOrText:me,hasContentType:de});i=e,w.webkit&&He(i,$e),w.ie&&(He(i,Le),u=ze,(s=i).on("PastePostProcess",function(e){u(s,e.node)}));return o=r,(a=e).ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:Ue(a,o)}),a.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:Ue(a,o)}),Se(e,r),function(e){var t=e.plugins.paste,n=e.getParam("paste_preprocess");n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=e.getParam("paste_postprocess");r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})}(e),Fe(e),Be(e,r,t),{clipboard:r,quirks:void 0}}var a,o,i,s,u,l,c,f})}();