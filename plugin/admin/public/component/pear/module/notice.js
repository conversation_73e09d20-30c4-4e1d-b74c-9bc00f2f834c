
(function(define) {
	define(['jquery'], function($) {
		return (function() {
			var $container;
			var listener;
			var toastId = 0;
			var toastType = {
				error: 'error',
				info: 'info',
				success: 'success',
				warning: 'warning'
			};

			var cssStyle = $(
				'<style type="text/css"> .toast-title{font-weight:bold}.toast-message{-ms-word-wrap:break-word;word-wrap:break-word}.toast-message a,.toast-message label{color:#fff}.toast-message a:hover{color:#ccc;text-decoration:none}.toast-close-button{position:relative;right:-0.3em;top:-0.3em;float:right;font-size:20px;font-weight:bold;color:#fff;-webkit-text-shadow:0 1px 0 #fff;text-shadow:0 1px 0 #fff;opacity:.8;-ms-filter:alpha(opacity=80);filter:alpha(opacity=80);line-height:1}.toast-close-button:hover,.toast-close-button:focus{color:#000;text-decoration:none;cursor:pointer;opacity:.4;-ms-filter:alpha(opacity=40);filter:alpha(opacity=40)}.rtl .toast-close-button{left:-0.3em;float:left;right:.3em}button.toast-close-button{padding:0;cursor:pointer;background:transparent;border:0;-webkit-appearance:none}.toast-top-center{top:0;right:0;width:100%}.toast-bottom-center{bottom:0;right:0;width:100%}.toast-top-full-width{top:0;right:0;width:100%}.toast-bottom-full-width{bottom:0;right:0;width:100%}.toast-top-left{top:12px;left:12px}.toast-top-right{top:12px;right:12px}.toast-bottom-right{right:12px;bottom:12px}.toast-bottom-left{bottom:12px;left:12px}#toast-container{position:fixed;z-index:999999;pointer-events:none}#toast-container *{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}#toast-container>div{position:relative;pointer-events:auto;overflow:hidden;margin:0 0 6px;padding:15px 15px 15px 50px;width:300px;-moz-border-radius:3px 3px 3px 3px;-webkit-border-radius:3px 3px 3px 3px;border-radius:3px 3px 3px 3px;background-position:15px center;background-repeat:no-repeat;-moz-box-shadow:0 0 12px #999;-webkit-box-shadow:0 0 12px #999;box-shadow:0 0 12px #999;color:#fff;opacity:.8;-ms-filter:alpha(opacity=80);filter:alpha(opacity=80)}#toast-container>div.rtl{direction:rtl;padding:15px 50px 15px 15px;background-position:right 15px center}#toast-container>div:hover{-moz-box-shadow:0 0 12px #000;-webkit-box-shadow:0 0 12px #000;box-shadow:0 0 12px #000;opacity:1;-ms-filter:alpha(opacity=100);filter:alpha(opacity=100);cursor:pointer}#toast-container>.toast-info{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=")!important}#toast-container>.toast-error{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=")!important}#toast-container>.toast-success{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==")!important}#toast-container>.toast-warning{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=")!important}#toast-container.toast-top-center>div,#toast-container.toast-bottom-center>div{width:300px;margin-left:auto;margin-right:auto}#toast-container.toast-top-full-width>div,#toast-container.toast-bottom-full-width>div{width:96%;margin-left:auto;margin-right:auto}.toast{background-color:#030303}.toast-success{background-color:#51a351}.toast-error{background-color:#bd362f}.toast-info{background-color:#2f96b4}.toast-warning{background-color:#f89406}.toast-progress{position:absolute;left:0;bottom:0;height:4px;background-color:#000;opacity:.4;-ms-filter:alpha(opacity=40);filter:alpha(opacity=40)}@media all and (max-width:240px){#toast-container>div{padding:8px 8px 8px 50px;width:11em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-0.2em;top:-0.2em}#toast-container .rtl .toast-close-button{left:-0.2em;right:.2em}}@media all and (min-width:241px) and (max-width:480px){#toast-container>div{padding:8px 8px 8px 50px;width:18em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-0.2em;top:-0.2em}#toast-container .rtl .toast-close-button{left:-0.2em;right:.2em}}@media all and (min-width:481px) and (max-width:768px){#toast-container>div{padding:15px 15px 15px 50px;width:25em}#toast-container>div.rtl{padding:15px 50px 15px 15px}}</style>'
			);
			$("body").append(cssStyle);

			var toastr = {
				clear: clear,
				remove: remove,
				error: error,
				getContainer: getContainer,
				info: info,
				options: {},
				subscribe: subscribe,
				success: success,
				version: '2.1.4',
				warning: warning
			};

			var previousToast;

			return toastr;

			function error(message, title, optionsOverride) {
				return notify({
					type: toastType.error,
					iconClass: getOptions().iconClasses.error,
					message: message,
					optionsOverride: optionsOverride,
					title: title
				});
			}

			function getContainer(options, create) {
				if (!options) {
					options = getOptions();
				}
				$container = $('#' + options.containerId);
				if ($container.length) {
					return $container;
				}
				if (create) {
					$container = createContainer(options);
				}
				return $container;
			}

			function info(message, title, optionsOverride) {
				return notify({
					type: toastType.info,
					iconClass: getOptions().iconClasses.info,
					message: message,
					optionsOverride: optionsOverride,
					title: title
				});
			}

			function subscribe(callback) {
				listener = callback;
			}

			function success(message, title, optionsOverride) {
				return notify({
					type: toastType.success,
					iconClass: getOptions().iconClasses.success,
					message: message,
					optionsOverride: optionsOverride,
					title: title
				});
			}

			function warning(message, title, optionsOverride) {
				return notify({
					type: toastType.warning,
					iconClass: getOptions().iconClasses.warning,
					message: message,
					optionsOverride: optionsOverride,
					title: title
				});
			}

			function clear($toastElement, clearOptions) {
				var options = getOptions();
				if (!$container) {
					getContainer(options);
				}
				if (!clearToast($toastElement, options, clearOptions)) {
					clearContainer(options);
				}
			}

			function remove($toastElement) {
				var options = getOptions();
				if (!$container) {
					getContainer(options);
				}
				if ($toastElement && $(':focus', $toastElement).length === 0) {
					removeToast($toastElement);
					return;
				}
				if ($container.children().length) {
					$container.remove();
				}
			}

			// internal functions

			function clearContainer(options) {
				var toastsToClear = $container.children();
				for (var i = toastsToClear.length - 1; i >= 0; i--) {
					clearToast($(toastsToClear[i]), options);
				}
			}

			function clearToast($toastElement, options, clearOptions) {
				var force = clearOptions && clearOptions.force ? clearOptions.force : false;
				if ($toastElement && (force || $(':focus', $toastElement).length === 0)) {
					$toastElement[options.hideMethod]({
						duration: options.hideDuration,
						easing: options.hideEasing,
						complete: function() {
							removeToast($toastElement);
						}
					});
					return true;
				}
				return false;
			}

			function createContainer(options) {
				$container = $('<div/>')
					.attr('id', options.containerId)
					.addClass(options.positionClass);

				$container.appendTo($(options.target));
				return $container;
			}

			function getDefaults() {
				return {
					tapToDismiss: true,
					toastClass: 'toast',
					containerId: 'toast-container',
					debug: false,

					showMethod: 'fadeIn', //fadeIn, slideDown, and show are built into jQuery
					showDuration: 300,
					showEasing: 'swing', //swing and linear are built into jQuery
					onShown: undefined,
					hideMethod: 'fadeOut',
					hideDuration: 1000,
					hideEasing: 'swing',
					onHidden: undefined,
					closeMethod: false,
					closeDuration: false,
					closeEasing: false,
					closeOnHover: true,

					extendedTimeOut: 1000,
					iconClasses: {
						error: 'toast-error',
						info: 'toast-info',
						success: 'toast-success',
						warning: 'toast-warning'
					},
					iconClass: 'toast-info',
					positionClass: 'toast-top-right',
					timeOut: 5000, // Set timeOut and extendedTimeOut to 0 to make it sticky
					titleClass: 'toast-title',
					messageClass: 'toast-message',
					escapeHtml: false,
					target: 'body',
					closeHtml: '<button type="button">&times;</button>',
					closeClass: 'toast-close-button',
					newestOnTop: true,
					preventDuplicates: false,
					progressBar: false,
					progressClass: 'toast-progress',
					rtl: false
				};
			}

			function publish(args) {
				if (!listener) {
					return;
				}
				listener(args);
			}

			function notify(map) {
				var options = getOptions();
				var iconClass = map.iconClass || options.iconClass;

				if (typeof(map.optionsOverride) !== 'undefined') {
					options = $.extend(options, map.optionsOverride);
					iconClass = map.optionsOverride.iconClass || iconClass;
				}

				if (shouldExit(options, map)) {
					return;
				}

				toastId++;

				$container = getContainer(options, true);

				var intervalId = null;
				var $toastElement = $('<div/>');
				var $titleElement = $('<div/>');
				var $messageElement = $('<div/>');
				var $progressElement = $('<div/>');
				var $closeElement = $(options.closeHtml);
				var progressBar = {
					intervalId: null,
					hideEta: null,
					maxHideTime: null
				};
				var response = {
					toastId: toastId,
					state: 'visible',
					startTime: new Date(),
					options: options,
					map: map
				};

				personalizeToast();

				displayToast();

				handleEvents();

				publish(response);

				if (options.debug && console) {
					console.log(response);
				}

				return $toastElement;

				function escapeHtml(source) {
					if (source == null) {
						source = '';
					}

					return source
						.replace(/&/g, '&amp;')
						.replace(/"/g, '&quot;')
						.replace(/'/g, '&#39;')
						.replace(/</g, '&lt;')
						.replace(/>/g, '&gt;');
				}

				function personalizeToast() {
					setIcon();
					setTitle();
					setMessage();
					setCloseButton();
					setProgressBar();
					setRTL();
					setSequence();
					setAria();
				}

				function setAria() {
					var ariaValue = '';
					switch (map.iconClass) {
						case 'toast-success':
						case 'toast-info':
							ariaValue = 'polite';
							break;
						default:
							ariaValue = 'assertive';
					}
					$toastElement.attr('aria-live', ariaValue);
				}

				function handleEvents() {
					if (options.closeOnHover) {
						$toastElement.hover(stickAround, delayedHideToast);
					}

					if (!options.onclick && options.tapToDismiss) {
						$toastElement.click(hideToast);
					}

					if (options.closeButton && $closeElement) {
						$closeElement.click(function(event) {
							if (event.stopPropagation) {
								event.stopPropagation();
							} else if (event.cancelBubble !== undefined && event.cancelBubble !== true) {
								event.cancelBubble = true;
							}

							if (options.onCloseClick) {
								options.onCloseClick(event);
							}

							hideToast(true);
						});
					}

					if (options.onclick) {
						$toastElement.click(function(event) {
							options.onclick(event);
							hideToast();
						});
					}
				}

				function displayToast() {
					$toastElement.hide();

					$toastElement[options.showMethod]({
						duration: options.showDuration,
						easing: options.showEasing,
						complete: options.onShown
					});

					if (options.timeOut > 0) {
						intervalId = setTimeout(hideToast, options.timeOut);
						progressBar.maxHideTime = parseFloat(options.timeOut);
						progressBar.hideEta = new Date().getTime() + progressBar.maxHideTime;
						if (options.progressBar) {
							progressBar.intervalId = setInterval(updateProgress, 10);
						}
					}
				}

				function setIcon() {
					if (map.iconClass) {
						$toastElement.addClass(options.toastClass).addClass(iconClass);
					}
				}

				function setSequence() {
					if (options.newestOnTop) {
						$container.prepend($toastElement);
					} else {
						$container.append($toastElement);
					}
				}

				function setTitle() {
					if (map.title) {
						var suffix = map.title;
						if (options.escapeHtml) {
							suffix = escapeHtml(map.title);
						}
						$titleElement.append(suffix).addClass(options.titleClass);
						$toastElement.append($titleElement);
					}
				}

				function setMessage() {
					if (map.message) {
						var suffix = map.message;
						if (options.escapeHtml) {
							suffix = escapeHtml(map.message);
						}
						$messageElement.append(suffix).addClass(options.messageClass);
						$toastElement.append($messageElement);
					}
				}

				function setCloseButton() {
					if (options.closeButton) {
						$closeElement.addClass(options.closeClass).attr('role', 'button');
						$toastElement.prepend($closeElement);
					}
				}

				function setProgressBar() {
					if (options.progressBar) {
						$progressElement.addClass(options.progressClass);
						$toastElement.prepend($progressElement);
					}
				}

				function setRTL() {
					if (options.rtl) {
						$toastElement.addClass('rtl');
					}
				}

				function shouldExit(options, map) {
					if (options.preventDuplicates) {
						if (map.message === previousToast) {
							return true;
						} else {
							previousToast = map.message;
						}
					}
					return false;
				}

				function hideToast(override) {
					var method = override && options.closeMethod !== false ? options.closeMethod : options.hideMethod;
					var duration = override && options.closeDuration !== false ?
						options.closeDuration : options.hideDuration;
					var easing = override && options.closeEasing !== false ? options.closeEasing : options.hideEasing;
					if ($(':focus', $toastElement).length && !override) {
						return;
					}
					clearTimeout(progressBar.intervalId);
					return $toastElement[method]({
						duration: duration,
						easing: easing,
						complete: function() {
							removeToast($toastElement);
							clearTimeout(intervalId);
							if (options.onHidden && response.state !== 'hidden') {
								options.onHidden();
							}
							response.state = 'hidden';
							response.endTime = new Date();
							publish(response);
						}
					});
				}

				function delayedHideToast() {
					if (options.timeOut > 0 || options.extendedTimeOut > 0) {
						intervalId = setTimeout(hideToast, options.extendedTimeOut);
						progressBar.maxHideTime = parseFloat(options.extendedTimeOut);
						progressBar.hideEta = new Date().getTime() + progressBar.maxHideTime;
					}
				}

				function stickAround() {
					clearTimeout(intervalId);
					progressBar.hideEta = 0;
					$toastElement.stop(true, true)[options.showMethod]({
						duration: options.showDuration,
						easing: options.showEasing
					});
				}

				function updateProgress() {
					var percentage = ((progressBar.hideEta - (new Date().getTime())) / progressBar.maxHideTime) * 100;
					$progressElement.width(percentage + '%');
				}
			}

			function getOptions() {
				return $.extend({}, getDefaults(), toastr.options);
			}

			function removeToast($toastElement) {
				if (!$container) {
					$container = getContainer();
				}
				if ($toastElement.is(':visible')) {
					return;
				}
				$toastElement.remove();
				$toastElement = null;
				if ($container.children().length === 0) {
					$container.remove();
					previousToast = undefined;
				}
			}

		})();
	});
}(typeof define === 'function' && define.amd ? define : function(deps, factory) {
	if (typeof module !== 'undefined' && module.exports) { //Node
		module.exports = factory(require('jquery'));
	} else if (window.layui && layui.define) {
		layui.define('jquery', function(exports) { //layui加载
			exports('toastr', factory(layui.jquery));
			exports('notice', factory(layui.jquery));
		});
	} else {
		window.toastr = factory(window.jQuery);
	}
}));
