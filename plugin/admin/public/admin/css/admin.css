html,
body,
.layui-layout {
	height: 100%;
}

.pear-admin .layui-header,
.pear-admin .layui-body,
.pear-admin .layui-logo,
.pear-admin .layui-side,
.pear-admin .layui-header .layui-layout-left {
	transition: all .3s;
}

.pear-admin.banner-layout .layui-side {
	top: 60px!important;
}

.pear-admin.banner-layout .layui-side .layui-logo {
	display: none;
}

.pear-admin.banner-layout .layui-header .layui-logo {
	display: inline-block;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin .layui-header.dark-theme .layui-layout-control .layui-this *{
	background-color: rgba(0,0,0,.1)!important;
}

.pear-admin.banner-layout .layui-header {
	z-index: 99999;
	width: 100%;
	left: 0px;
}

.pear-admin.banner-layout .layui-header .layui-layout-left {
	left: 230px;
}

.pear-admin.banner-layout .layui-header .layui-logo .title {
	top: 2px;
}

.pear-admin.banner-layout .layui-header .layui-layout-control {
	display: inline-block;
	left: 370px;
}

.pear-admin.banner-layout .layui-header.dark-theme {
	box-shadow: 2px 0 6px rgb(0 21 41 / 35%);
}

.pear-admin .layui-header .layui-logo {
	display: none;
}

.pear-admin .layui-logo .title {
	font-size: 20px;
}

.pear-admin .layui-layout-right .layui-nav-child {
	border: 1px solid whitesmoke;
	border-radius: 4px;
	width: auto;
	left: auto;
	right: -23px;
}

.pear-admin .layui-header {
	left: 230px;
	width: calc(100% - 230px);
	background-color: white;
	border-bottom: 1px solid whitesmoke;
}

.pear-admin .layui-layout-control {
	left: 140px;
	position: absolute;
}

.pear-admin .layui-layout-control .layui-nav {
	padding: 0px;
}

.pear-admin .layui-logo {
	width: 230px;
	height: 59px;
	line-height: 59px;
	position: relative;
	background-color: #001529;
	border-bottom: 1px solid rgba(0, 0, 0, .12);
}

.pear-admin .layui-logo img {
	width: 34px;
	height: 34px;
}

.pear-admin .layui-logo .title {
	font-size: 21px;
	font-weight: 550;
	color: #5FB878;
	position: relative;
	top: 5px;
	margin-left: 5px;
}

.pear-admin .layui-logo .logo {
	display: none;
}

.pear-admin .layui-side {
	top: 0px;
	width: 230px;
	box-shadow: 2px 0 6px rgba(0, 21, 41, .20);
	z-index: 9999;
}

.pear-admin .layui-side-scroll::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

.pear-admin .layui-side-scroll {
	height: calc(100% - 60px) !important;
	background-color: #001529;
	width: 247px;

}

.pear-admin .layui-header .layui-nav .layui-nav-item>a {
	color: black;
	font-size: 15px;
}

.pear-admin .layui-body {
	left: 230px;
	bottom: 0px;
	padding-bottom: 0px;
}

.pear-admin .layui-layout-left {
	left: 0px;
}

.pear-admin .layui-footer {
	position: absolute;
    display: flex;
    justify-content: space-between;
	left: 230px;
	background: #fff;
	border-top: 1px solid #F2F2F2;
	box-shadow: none;
	-webkit-transition: left .3s;
	transition: left .3s;
	overflow: hidden;
	color: #3c3c3cb3;
	font-weight: 300;
	font-size: 13.6px;
}

.pear-admin .layui-footer.close {
	display: none;
}

/** 收缩布局 */
.pear-mini .layui-side .layui-logo .title {
	display: none;
}

.pear-mini .layui-side .layui-logo .logo {
	display: inline-block;
}

.pear-mini .layui-side {
	width: 60px;
}

.pear-mini .layui-header {
	left: 60px;
	width: calc(100% - 60px);
}

.pear-mini .layui-body {
	left: 60px;
}

.pear-mini .layui-side .layui-logo {
	width: 60px;
}

.pear-mini .layui-footer {
	left: 60px;
}

.pear-mini .layui-nav-tree .layui-nav-item span {
	display: none;
}

.pear-mini .bottom-nav li {
	width: 100% !important;
}

.pear-mini .layui-side-scroll {
	height: calc(100% - 60px);
}

.pear-admin .layui-header .layui-nav .layui-nav-bar {
	top: 0px !important;
	height: 2px !important;
	background-color: #5FB878;
}

.pear-admin .layui-header .layui-nav .layui-this:after {
	display: none;
}

.pear-admin .layui-header .layui-nav-more {
	display: none;
}

.pear-collapsed-pe {
	display: none;
	width: 50px;
	position: absolute;
	z-index: 400000;
	bottom: 30px;
	right: 30px;
	background-color: #5FB878 !important;
	height: 50px;
	line-height: 50px;
	text-align: center;
	border-radius: 50px;
	box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
}

.pear-collapsed-pe a {
	color: white !important;
}

@media screen and (min-width: 768px) {
	.layui-hide-sm {
		display: inline-block !important;
	}
}

@media screen and (min-width: 769px) {
	.layui-hide-sm {
		display: none !important;
	}
}

/** 新增兼容 */
@media screen and (max-width:768px) {
	.collapse {
		display: none !important;
	}

	.pear-collapsed-pe {
		display: inline-block !important;
	}

	.layui-layout-control {
		left: 45px !important;
	}

	.layui-layout-left {
		padding-left: 10px;
		padding-right: 10px;
	}

	.pear-mini .bottom-nav {
		display: none;
	}

	.pear-mini .layui-side-scroll {
		height: calc(100% - 62px);
	}

	.pear-mini .layui-side {
		width: 0px;
	}

	.pear-mini .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-mini .layui-body {
		left: 0px;
	}

	.pear-mini .layui-footer {
		left: 0px;
	}

	.pear-mini .layui-logo {
		width: 0px;
	}

	.pear-admin .layui-body {
		left: 0px;
	}

	.pear-admin .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-admin .pear-cover {
		width: 100%;
		height: 100%;
		background-color: #1E1E1E;
		display: block;
		position: absolute;
		z-index: 1000;
		opacity: 0;
		margin-top: -60px;
	}

	.pear-mini .pear-cover {
		display: none;
	}
}

@-webkit-keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(2000px);
		transform: translateX(2000px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		transform: translateX(0)
	}
}

@keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(800px);
		-ms-transform: translateX(800px);
		transform: translateX(800px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		-ms-transform: translateX(0);
		transform: translateX(0)
	}
}

.layer-anim-right {
	-webkit-animation: am-horizontal-roll_show .6s ease-out;
	animation: am-horizontal-roll_show .6s ease-out;

}

/** 侧边主题 (亮) */
.light-theme.layui-side {
	box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05) !important;
}

.light-theme.layui-side .layui-logo {
	background-color: white !important;
	color: black !important;
	border-bottom: 1px whitesmoke solid;
}

.light-theme.layui-side .layui-side-scroll {
	background-color: white !important;
	color: black !important;
}

.dark-theme.layui-header {
	border-bottom: none;
	background-color: #001529;
	color: whitesmoke;
}

.dark-theme.layui-header li>a{
	color: whitesmoke!important;
}

.dark-theme.layui-header .layui-logo {
	box-shadow: none;
	border: none;
}


/** 顶部主题 (白) */
.light-theme.layui-header .layui-logo {
	background-color: white;
	border: none;
	box-shadow: none;
}

/** 主题面板 */
.pearone-color .set-text {
	height: 42px;
	line-height: 42px;
}

.pearone-color .color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.pearone-color .color-content {
	padding: 15px 10px 0 20px;
}

.pearone-color .color-content ul {
	list-style: none;
	padding: 0px;
}

.pearone-color .color-content ul li {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 70px;
	height: 50px;
	margin: 0 20px 20px 0;
	padding: 2px 2px 2px 2px;
	background-color: #f2f2f2;
	cursor: pointer;
	font-size: 12px;
	color: #666;
}

.pearone-color .color-content li.layui-this:after,
.pearone-color .color-content li:hover:after {
	width: 100%;
	height: 100%;
	padding: 4px;
	top: -5px;
	left: -5px;
	border: #5FB878 2px solid;
	opacity: 1;
	border-radius: 4px;
}

.pearone-color .color-content li:after {
	content: '';
	position: absolute;
	z-index: 20;
	top: 50%;
	left: 50%;
	width: 1px;
	height: 0;
	border: 2px solid #F2F2F2;
	transition: all .3s;
	-webkit-transition: all .3s;
	opacity: 0;
}

.select-color {
	margin-bottom: 30px;
}

.select-color .select-color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.select-color .select-color-content {
	padding: 20px 0 0px 0px;
	margin-bottom: 4px;
}

.select-color .select-color-content .select-color-item {
	background-color: gray;
	width: 30px;
	height: 30px;
	border-radius: 3px;
	float: left;
	margin-left: 20px;
	color: white;
	font-size: 18px;
	text-align: center;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
	line-height: 30px;
}

.message .layui-tab-title li:not(:last-child) {
	border-right: 1px solid #eee;
}

/* 搜索面板 */
.menu-search-content .layui-input {
	padding-left: 30px;
}

.menu-search-content {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

.menu-search-input-wrapper {
	width: 100%;
	padding: 15px 15px;
}

.menu-search-no-data {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 122px;
	align-items: center;
}

.menu-search-list {
	width: 100%;
	padding: 5px 15px;
}

.menu-search-list li {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: nowrap;
	height: 50px;
	margin-bottom: 8px;
	padding: 0px 10px;
	color: currentColor;
	font-size: 14px;
	border-radius: 4px;
	box-shadow: 0 1px 3px #d4d9e1;
	cursor: pointer;
	background-color: #fff;
}

.menu-search-list li:hover {
	background-color: #5FB878;
	color: white;
}

.menu-search-list li.this {
	background-color: #5FB878;
	color: white;
}

/* 搜索面板结束 */