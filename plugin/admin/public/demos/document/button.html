<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>按钮组件</title>
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	</head>
	<body class="pear-container">
		<div class="layui-row layui-col-space10">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						开发环境 
					</div>
					<div class="layui-card-body">
						Pear Button 参考 Element UI 样式 ，提供 Button 服务
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								&lt;link rel="stylesheet" href="component/pear/css/pear.css" />
								或
								&lt;link rel="stylesheet" href="component/pear/css/pear-module/button.css" />
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						简单使用
					</div>
					<div class="layui-card-body">
						<button class="pear-btn">Default Button</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-primary">Primary Button</button>&nbsp;&nbsp;
						<br>
						<br>
						<button class="pear-btn pear-btn-primary"> Button-Primary</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-success"> Button-Success</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-danger"> Button-danger</button>&nbsp;&nbsp;
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								<button class="pear-btn"> Default Button </button>
								<button class="pear-btn" dashed> Dashed Button </button>
								<button class="pear-btn pear-btn-primary"> Primary Button </button>							
								<button class="pear-btn pear-btn-primary"> Button-Primary </button>
								<button class="pear-btn pear-btn-success"> Button-Success </button>
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						简约样式
					</div>
					<div class="layui-card-body">
						<button plain class="pear-btn pear-btn-primary"> Button-Primary</button>&nbsp;&nbsp;
						<button plain class="pear-btn pear-btn-success"> Button-Success</button>&nbsp;&nbsp;
						<button plain class="pear-btn pear-btn-danger"> Button-Danger</button>&nbsp;&nbsp;
						<button plain class="pear-btn pear-btn-warming"> Button-Warming</button>&nbsp;&nbsp;
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								<button class="pear-btn"> Default Button </button>
								<button class="pear-btn" dashed> Dashed Button </button>
								<button class="pear-btn pear-btn-primary"> Primary Button </button>							
								<button class="pear-btn pear-btn-primary"> Button-Primary </button>
								<button class="pear-btn pear-btn-success"> Button-Success </button>
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						按钮大小
					</div>
					<div class="layui-card-body">
						<button class="pear-btn pear-btn-primary pear-btn-lg"> Button-Lg</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-primary"> Button-Default</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-primary pear-btn-sm"> Button-Sm</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-primary pear-btn-xs"> Button-Xs</button>&nbsp;&nbsp;
						<br>
						<br>
						<button class="pear-btn pear-btn-warming pear-btn-lg"> Button-Lg</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-warming"> Button-Default</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-warming pear-btn-sm"> Button-Sm</button>&nbsp;&nbsp;
						<button class="pear-btn pear-btn-warming pear-btn-xs"> Button-Xs</button>&nbsp;&nbsp;
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								<button class="pear-btn pear-btn-primary pear-btn-lg"> Button-Lg </button>&nbsp;&nbsp;
								<button class="pear-btn pear-btn-primary"> Button-Default </button>&nbsp;&nbsp;
								<button class="pear-btn pear-btn-primary pear-btn-sm"> Button-Sm </button>&nbsp;&nbsp;
								<button class="pear-btn pear-btn-primary pear-btn-xs"> Button-Xs </button>&nbsp;&nbsp;
							</pre>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-row layui-col-space10">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						按钮组
					</div>
					<div class="layui-card-body">
						<div class="pear-btn-group">
							<button class="pear-btn pear-btn-primary" round> Button-One</button>
							<button class="pear-btn pear-btn-primary" round> Button-Two</button>
							<button class="pear-btn pear-btn-primary" round> Button-Three</button>
						</div>
						<br>
						<br>
						<div class="pear-btn-group">
							<button class="pear-btn"> Button-One </button>
							<button class="pear-btn"> Button-Two </button>
							<button class="pear-btn"> Button-Three </button>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								<div class="pear-btn-group">
								    <button class="pear-btn"> Button-One </button>
								    <button class="pear-btn"> Button-Two </button>
								    <button class="pear-btn"> Button-Three </button>
								</div>
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						Load 自动
					</div>
					<div class="layui-card-body">
						<button class="pear-btn pear-btn-primary" load> 加载 600 ms </button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(["button"], function() {
								    var button = layui.button;
								    
									button.load({
								        elem:'[load]',
								        time: 600,
								        done: function(){
									        popup.success("加载完成");
								        }
								    })
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						Load 手动
					</div>
					<div class="layui-card-body">

						<button class="pear-btn pear-btn-primary" loading> 开始 </button>

						<button class="pear-btn pear-btn-danger" stop> 停止 </button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(["button"], function() {
								    var button = layui.button;
								    
									var dom = button.load({
								        elem:'[load]',
								    })
									
								    dom.stop(function() {
								        popup.failure("已停止");
								    });
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						本页跳转
					</div>
					<div class="layui-card-body">
						<button class="pear-btn pear-btn-primary" to> 本页跳转 </button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(["jquery"], function() {
								    var $ = layui.jquery;
								    
								    $("[to]").click(function(){
								        top.layui.frame.changePageByElement("content","http://www.baidu.com","百度一下",true)
								    })
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
		<script>
			layui.use(['element', 'code', 'jquery', "button", "popup"], function() {
				var element = layui.element;
				var $ = layui.jquery;
				var popup = layui.popup;
				var button = layui.button;
				
				layui.code();
				
				$("[load]").click(function() {
					button.load({
						elem: '[load]',
						time: 600,
						done: function() {
							popup.success("加载完成");
						}
					})
				})

				var dom;

				$("[loading]").click(function() {
					dom = button.load({
						elem: '[loading]'
					})
				})

				$("[stop]").click(function() {
					dom.stop(function() {
						popup.failure("已停止");
					});
				})
				
				$("[to]").click(function(){
					top.layui.admin.jump(14,"百度一下","http://www.bing.com",true)
				})
			})
		</script>
	</body>
</html>
