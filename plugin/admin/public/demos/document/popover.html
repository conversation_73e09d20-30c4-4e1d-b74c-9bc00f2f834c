<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>汽泡组件</title>
    <link rel="stylesheet" href="../../component/pear/css/pear.css" />
</head>
<body class="pear-container">
<div>
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">开发环境</div>
                <div class="layui-card-body">
                    popover 用于 汽泡显示 场景
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">显示代码</h2>
                    <div class="layui-colla-content">
                        <pre class="layui-code" lay-encode="true">
                            &lt;link rel="stylesheet" href="component/pear/css/pear.css" />
                             并
                            &lt;script src="component/layui/layui.js">&lt;/script>
                             并
                            &lt;script src="component/pear/pear.js">&lt;/script>
                        </pre>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">汽泡控制</div>
                <div class="layui-card-body">
                    <button class="popover-show pear-btn pear-btn-primary">显示</button>
                    <button class="popover-hide pear-btn pear-btn-success">隐藏</button>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">显示代码</h2>
                    <div class="layui-colla-content">
								<pre class="layui-code" lay-encode="true">
									layui.use(['popover', 'jquery', 'layer', 'code'], function() {
									   var popover = layui.popover;
    popover.show('#el1');
    popover.hide('#el1'); //或 $('#el1').webuiPopover('hide');


									})
								</pre>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    不同方式
                </div>
                <div class="layui-card-body">
                    <button class="toast-top-left-btn pear-btn" id="el1">手动</button>
                    <button class="toast-top-center-btn pear-btn" id="el2">回调</button>
                    <button class="toast-top-center-btn pear-btn" id="el3">iframe</button>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">显示代码</h2>
                    <div class="layui-colla-content">
								<pre class="layui-code" lay-encode="true">
									layui.use(['toast', 'jquery', 'layer', 'code'], function() {
									    var popover = layui.popover;
    popover.create('#el1',{title:' hello popover-manual',content:'这里显示内容',trigger:'manual',placement:'auto',
        animation:'pop',
        closeable:true,
        delay: {
            //show and hide delay time of the popover, works only when trigger is 'hover',the value can be number or object
            show: null,
            hide: 100
        },
        opacity:0.98,
        type:'html',//content type, values:'html','iframe','async'
    });

									})
								</pre>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    隐藏
                </div>
                <div class="layui-card-body">
                    <button class="pear-btn" id="closeAll">隐藏全部</button>
                </div>
            </div>
        </div>
        <div class="layui-col-md12">
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">显示代码</h2>
                    <div class="layui-colla-content">
								<pre class="layui-code" lay-encode="true">
									layui.use(['popover', 'jquery', 'layer', 'code'], function() {
									    var popover = layui.popover;
									    popover.hideAll();
									})
								</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script src="../../component/layui/layui.js"></script>
<script src="../../component/pear/pear.js"></script>
<script>
    layui.use(['popover', 'jquery', 'layer', 'code', 'element'], function() {
        var layer = layui.layer;
        var $ = layui.jquery;
        var popover = layui.popover;
        var element = layui.element;
        layui.code();
        $(".popover-show").click(function(e) {
            popover.show('#el1');
        })
        $("#el1").click(function(e) {
            popover.show('#el1');
        })

        $(".popover-hide").click(function(e){
            popover.hide('#el1'); //或 $('#el1').webuiPopover('hide');
        })
        $("#closeAll").click(function(e){
            popover.hideAll();
        });

        //html 事件 手动
        //用法参照  https://github.com/sandywalker/webui-popover
        //增加功能  opacity [0,1] 可指定 popover 透明度
        popover.create('#el1',{title:' hello popover-manual',content:'这里显示内容',trigger:'manual',
            animation:'pop',
            closeable:true,
            placement:'auto',
            delay: {
                //show and hide delay time of the popover, works only when trigger is 'hover',the value can be number or object
                show: null,
                hide: 100
            },
            opacity:0.98,
            type:'html',//content type, values:'html','iframe','async'
        })

        //html 事件 hover
        popover.create('#el2',{title:' hello popover-hover',content:'<div class="layui-tab layui-tab-brief" lay-filter="test">\n' +
                '  <ul class="layui-tab-title">\n' +
                '    <li class="layui-this" lay-id="11">网站设置</li>\n' +
                '    <li lay-id="22">用户管理</li>\n' +
                '    <li lay-id="33">权限分配</li>\n' +
                '    <li lay-id="44">商品管理</li>\n' +
                '    <li lay-id="55">订单管理</li>\n' +
                '  </ul>\n' +
                '  <div class="layui-tab-content" style="height: 100px;">\n' +
                '    <div class="layui-tab-item layui-show">内容不一样是要有，因为你可以获得 tab 事件</div>\n' +
                '    <div class="layui-tab-item">内容2</div>\n' +
                '    <div class="layui-tab-item">内容3</div>\n' +
                '    <div class="layui-tab-item">内容4</div>\n' +
                '    <div class="layui-tab-item">内容5</div>\n' +
                '  </div>\n' +
                '</div> ',trigger:'hover',
            animation:'pop',
            delay: {
                //show and hide delay time of the popover, works only when trigger is 'hover',the value can be number or object
                show: null,
                hide: 100
            },
            placement:'top-right',
            opacity:0.98,
            onShow: function($element) {
                //console.log("onShow",$element);
                element.on('tab(test)', function(elem){
                    location.hash = 'test='+ $(this).attr('lay-id');
                    console.log(location.hash);
                })

            },
        });

        popover.create('#el3',{title:' hello popover-iframe',trigger:'hover',
            animation:'pop',
            closeable:true,
            placement:'auto',
            delay: {
                //show and hide delay time of the popover, works only when trigger is 'hover',the value can be number or object
                show: null,
                hide: 100
            },
            opacity:0.98,
            type:'iframe',//content type, values:'html','iframe','async'
            url:'http://cn.bing.com/',
            width:500,
            height:400
        })





    });
</script>

</html>
