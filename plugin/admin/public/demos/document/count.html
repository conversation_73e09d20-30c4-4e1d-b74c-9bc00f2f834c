<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>数字滚动</title>
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	</head>
	<body class="pear-container">
		<div class="layui-row layui-col-space10">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						开发环境
					</div>
					<div class="layui-card-body">
						Pear Count 数据滚动特效
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								&lt;script src="component/layui/layui.js">&lt;/script>
								 并
								&lt;script src="component/pear/pear.js">&lt;/script>
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						简单使用
					</div>
					<div class="layui-card-body">
						<h2 id="number1">0</h2>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['count'], function() {
								    var count = layui.count;
									
								    count.up("number1", {
								        time: 8000,
								        num: 4540.34,
								        regulator: 100
								    })
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">
						保留小数
					</div>
					<div class="layui-card-body">
						<h2 id="number2">0</h2>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
							layui.use(['count'], function() {
							    var count = layui.count;
								
							    count.up("number2", {
							        time: 8000,
							        num: 4540.34,
							        bit: 2,
							        regulator: 100
							    })
							})
						</pre>
						</div>
					</div>
				</div>
			</div>
		</div>


		</div>
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
		<script>
			layui.use(['element', 'code', 'count'], function() {
				var element = layui.element;
				var count = layui.count;

				count.up("number1", {
					time: 8000,
					num: 4540.34,
					regulator: 100
				})

				count.up("number2", {
					time: 8000,
					num: 9832.34,
					bit: 2,
					regulator: 100
				})


				layui.code();
			})
		</script>
	</body>
</html>
