
<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <title>浏览页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body class="pear-container">
    
        <!-- 顶部查询表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form top-search-from">

                    <div class="layui-form-item">
                        <label class="layui-form-label">类别</label>
                        <div class="layui-input-block">
                            <div name="category" id="category" value="" ></div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">扩展名</label>
                        <div class="layui-input-block">
                            <input type="text" name="ext" value="" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">名字</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">上传时间</label>
                        <div class="layui-input-block">
                            <div class="layui-input-block" id="created_at">
                                <input type="text" autocomplete="off" name="created_at[]" id="created_at-date-start" class="layui-input inline-block" placeholder="开始时间">
                                -
                                <input type="text" autocomplete="off" name="created_at[]" id="created_at-date-end" class="layui-input inline-block" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item layui-inline">
                        <label class="layui-form-label"></label>
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="table-query">
                            <i class="layui-icon layui-icon-search"></i>查询
                        </button>
                        <button type="reset" class="pear-btn pear-btn-md" lay-submit lay-filter="table-reset">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                    <div class="toggle-btn">
                        <a class="layui-hide">展开<i class="layui-icon layui-icon-down"></i></a>
                        <a class="layui-hide">收起<i class="layui-icon layui-icon-up"></i></a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="data-table" lay-filter="data-table"></table>
            </div>
        </div>

        <!-- 表格顶部工具栏 -->
        <script type="text/html" id="table-toolbar">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" permission="app.admin.upload.insert">
                <i class="layui-icon layui-icon-add-1"></i>新增
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove" permission="app.admin.upload.delete">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
        </script>

        <!-- 表格行工具栏 -->
        <script type="text/html" id="table-bar">
            <button class="pear-btn pear-btn-xs pear-btn-primary" lay-event="select">选择</button>
        </script>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script src="/app/admin/admin/js/common.js"></script>
        <script>

            // 相关接口
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/admin/upload/select";
            const UPDATE_API = "/app/admin/upload/update";
            const DELETE_API = "/app/admin/upload/delete";
            const INSERT_URL = "/app/admin/upload/insert";
            const UPDATE_URL = "/app/admin/upload/update";
            
            // 表格渲染
            layui.use(["table", "form", "common", "popup", "util"], function() {
                let table = layui.table;
                let form = layui.form;
                let $ = layui.$;
                let common = layui.common;
                let util = layui.util;

                // 字段 上传时间 created_at
                layui.use(["laydate"], function() {
                    layui.laydate.render({
                        elem: "#created_at",
                        range: ["#created_at-date-start", "#created_at-date-end"],
                        type: "datetime",
                    });
                })

                // 字段 类别 category
                layui.use(["jquery", "xmSelect"], function() {
                    layui.$.ajax({
                        url: "/app/admin/dict/get/upload",
                        dataType: "json",
                        success: function (res) {
                            let value = layui.$("#category").attr("value");
                            let initValue = value ? value.split(",") : [];
                            layui.xmSelect.render({
                                el: "#category",
                                name: "category",
                                initValue: initValue,
                                data: res.data,
                                model: {"icon":"hidden","label":{"type":"text"}},
                                clickClose: true,
                                radio: true,
                                on: function(data){
                                    let value = data.arr[0] ? data.arr[0].value : "";
                                    table.reload("data-table", {
                                        where: {category: value}
                                    })
                                },
                            })
                        }
                    });
                });
                
				// 表头参数
				let cols = [
					{
						type: "checkbox"
					},{
						title: "主键",
						field: "id",
                        width: 80,
						sort: true,
					},{
						title: "名字",
						field: "name",
					},{
						title: "文件",
						field: "url",
						templet: function (d) {
                            if (["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"].indexOf(d.ext.toLowerCase()) !== -1) {
                                return '<img src="' + encodeURI(d['url']) + '" style="max-width:32px;max-height:32px;" />';
                            }
							return '<a href="' + encodeURI(d['url']) + '" target="_blank">' + util.escape(d['url']) + '</a>';
						}
					},{
						title: "管理员id",
						field: "admin_id",
						hide: true,
					},{
						title: "用户id",
						field: "user_id",
						hide: true,
					},{
						title: "文件大小",
						field: "file_size",
                        templet: function (d) {
                            return formatSize(d.file_size);
                        }
					},{
						title: "mime类型",
						field: "mime_type",
                        hide: true,
					},{
						title: "图片宽度",
						field: "image_width",
					},{
						title: "图片高度",
						field: "image_height",
					},{
						title: "扩展名",
						field: "ext",
					},{
						title: "存储位置",
						field: "storage",
						hide: true,
					},{
						title: "上传时间",
						field: "created_at",
						hide: true,
					},{
						title: "类别",
						field: "category",
						templet: function (d) {
							let field = "category";
							if (typeof d[field] == "undefined") return "";
							let items = [];
							layui.each((d[field] + "").split(","), function (k , v) {
								items.push(apiResults[field][v] || v);
							});
							return util.escape(items.join(","));
						}
					},{
						title: "更新时间",
						field: "updated_at",
						hide: true,
					},{
                        title: "操作",
                        toolbar: "#table-bar",
                        align: "center",
                        fixed: "right",
                        width: 130,
                    }
				];
				
				// 渲染表格
				function render()
				{
                    // 查询指定后缀
                    let searchExt = layui.url().search.ext;
                    if (searchExt) {
                        layui.$('.top-search-from input[name="ext"]').val(searchExt);
                    }
				    table.render({
				        elem: "#data-table",
				        url: SELECT_API,
				        page: true,
				        cols: [cols],
				        skin: "line",
				        size: "lg",
				        toolbar: "#table-toolbar",
				        autoSort: false,
                        where: {ext: searchExt},
				        defaultToolbar: [{
				            title: "刷新",
				            layEvent: "refresh",
				            icon: "layui-icon-refresh",
				        }, "filter", "print", "exports"],
                        done: function () {
                            layer.photos({photos: 'div[lay-id="data-table"]', anim: 5});
                        }
				    });
				}
				
				// 获取表格中下拉或树形组件数据
				let apis = [];
				apis.push(["category", "/app/admin/dict/get/upload"]);
				let apiResults = {};
				apiResults["category"] = [];
				let count = apis.length;
				layui.each(apis, function (k, item) {
				    let [field, url] = item;
				    $.ajax({
				        url: url,
				        dateType: "json",
				        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
				            function travel(items) {
				                for (let k in items) {
				                    let item = items[k];
				                    apiResults[field][item.value] = item.name;
				                    if (item.children) {
				                        travel(item.children);
				                    }
				                }
				            }
				            travel(res.data);
				        },
				        complete: function () {
				            if (--count === 0) {
				                render();
				            }
				        }
				    });
				});
				if (!count) {
				    render();
				}
				
                // 编辑删除或选择行事件
                table.on("tool(data-table)", function(obj) {
                    if (obj.event === "remove") {
                        remove(obj);
                    } else if (obj.event === "edit") {
                        edit(obj);
                    } else if (obj.event === "select") {
                        select(obj);
                    }
                });

                // 表格顶部工具栏事件
                table.on("toolbar(data-table)", function(obj) {
                    if (obj.event === "add") {
                        add();
                    } else if (obj.event === "refresh") {
                        refreshTable();
                    } else if (obj.event === "batchRemove") {
                        batchRemove(obj);
                    }
                });

                // 表格顶部搜索事件
                form.on("submit(table-query)", function(data) {
                    table.reload("data-table", {
                        page: {
                            curr: 1
                        },
                        where: data.field
                    })
                    return false;
                });
                
                // 表格顶部搜索重置事件
                form.on("submit(table-reset)", function(data) {
                    table.reload("data-table", {
                        where: []
                    })
                });

                // 表格排序事件
                table.on("sort(data-table)", function(obj){
                    table.reload("data-table", {
                        initSort: obj,
                        scrollPos: "fixed",
                        where: {
                            field: obj.field,
                            order: obj.type
                        }
                    });
                });

                // 表格新增数据
                let add = function() {
                    layer.open({
                        type: 2,
                        title: "新增",
                        shade: 0.1,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"380px"],
                        content: INSERT_URL
                    });
                }

                // 表格编辑数据
                let edit = function(obj) {
                    let value = obj.data[PRIMARY_KEY];
                    layer.open({
                        type: 2,
                        title: "修改",
                        shade: 0.1,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"380px"],
                        content: UPDATE_URL + "?" + PRIMARY_KEY + "=" + value
                    });
                }

                // 删除一行
                let remove = function(obj) {
                    return doRemove(obj.data[PRIMARY_KEY]);
                }

                // 删除多行
                let batchRemove = function(obj) {
                    let checkIds = common.checkField(obj, PRIMARY_KEY);
                    if (checkIds === "") {
                        layui.popup.warning("未选中数据");
                        return false;
                    }
                    doRemove(checkIds.split(","));
                }

                // 执行删除
                let doRemove = function (ids) {
                    let data = {};
                    data[PRIMARY_KEY] = ids;
                    layer.confirm("确定删除?", {
                        icon: 3,
                        title: "提示"
                    }, function(index) {
                        layer.close(index);
                        let loading = layer.load();
                        $.ajax({
                            url: DELETE_API,
                            data: data,
                            dataType: "json",
                            type: "post",
                            success: function(res) {
                                layer.close(loading);
                                if (res.code) {
                                    return layui.popup.failure(res.msg);
                                }
                                return layui.popup.success("操作成功", refreshTable);
                            }
                        })
                    });
                }

                // 选择行
                let select = function (obj) {
                    let index = parent.layer.getFrameIndex(window.name);
                    let callback = parent.layui.$("#layui-layer" + index).data("callback");
                    callback(obj.data);
                    parent.layer.close(index);
                };

                // 刷新表格数据
                window.refreshTable = function() {
                    table.reloadData("data-table", {
                        scrollPos: "fixed",
                        done: function (res, curr) {
                            if (curr > 1 && res.data && !res.data.length) {
                                curr = curr - 1;
                                table.reloadData("data-table", {
                                    page: {
                                        curr: curr
                                    },
                                })
                            }
                        }
                    });
                }

                // 格式化文件大小
                let formatSize = function(value) {
                    if(null == value || "" === value){
                        return "0 Bytes";
                    }
                    let unitArr = ["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"];
                    let index = 0;
                    let srcSize = parseFloat(value);
                    index = Math.floor(Math.log(srcSize) / Math.log(1024));
                    let size =srcSize / Math.pow(1024, index);
                    size = size.toFixed(2);
                    return size + unitArr[index];
                }

            })

        </script>
    </body>
</html>
