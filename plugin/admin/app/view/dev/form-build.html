<!doctype html>
<html lang="zh-cn">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title>表单生成器</title>
		<link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
		<style>
			html,body{background-color: whitesmoke}
			.layui-fluid{margin-top: 15px;}
			.content{min-height: 1200px;}
			.nav{text-align: center;}
			.nav button{margin-bottom: 3px;width: 100%;margin-top: 3px;margin-bottom: 3px;border-radius: 1px;}
			.nav button:hover{background-color: #5FB878;border: 1px solid #5FB878;color: white;}
			.layui-card-body .layui-btn+.layui-btn{margin-left: 0px;}
			.code-show{min-height: 700px;}
			.js-show{min-height: 360px;}
			.layui-card-body {padding: 10px;}
			.button{line-height: 100% !important;}
			.content .pear-btn-md, .content .pear-btn-sm, .content .pear-btn-xs, .content .pear-btn {
				line-height: 100%;
				letter-spacing: 2px;
				padding: 0 15px;
				font-weight: 400;
				font-size: 14px;
			}
		</style>
	</head>
	<body>
		<div class="layui-fluid">
			<div class="layui-row layui-col-space10">
				<div class="layui-col-md1">
					<div class="layui-card nav click-but">
						<div class="layui-card-header">长</div>
						<div class="layui-card-body">
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="text">输入框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="password">密码框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="select">选择框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="checkbox_a">复选框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="checkbox_b">开关</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="radio">单选框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="textarea">文本域</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="icon">图标</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="multiSelect">下拉多选</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="treeSelectOne">树单选</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="tree">树多选</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="upload">上传文件</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="uploadImg">上传图片</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="submit">提交</button>
						</div>
					</div>
					<div class="layui-card nav">
						<div class="layui-card-header">短</div>
						<div class="layui-card-body">
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="text">输入框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="password">密码框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="select">选择框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="checkbox_a">复选框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="checkbox_b">开关</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="radio">单选框</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="textarea">文本域</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="icon">图标</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="multiSelect">下拉多选</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="treeSelectOne">树单选</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="tree">树多选</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="upload">上传文件</button>
							<button class="pear-btn pear-btn-sm" plain data-size="inline" data-type="uploadImg">上传图片</button>
							<button class="pear-btn pear-btn-sm" plain data-size="block" data-type="submit">提交</button>
						</div>
					</div>
					<div class="layui-card nav">
						<div class="layui-card-body">
							<button class="pear-btn pear-btn-danger pear-btn-sm del-form" data-type="del"> <i class="layui-icon layui-font-16">&#xe640;</i></button>
						</div>
					</div>

				</div>
				<div class="layui-col-md5">
					<div class="layui-card content">
						<div class="layui-card-header">
							view
						</div>
						<div class="layui-card-body code">
							<form class="layui-form" action="" onsubmit="return false">
							</form>
						</div>
					</div>
				</div>
				<div class="layui-col-md6">
					<div class="layui-card r-code-html">
						<div class="layui-card-header">html</div>
						<div class="layui-card-body">
							<textarea name="" class="layui-textarea code-show"></textarea>
						</div>
					</div>
					<div class="layui-card r-code-js">
						<div class="layui-card-header">code</div>
						<div class="layui-card-body">
							<textarea name="" class="layui-textarea js-show"></textarea>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
	<script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
	<script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
	<script>
		layui.use("design");
	</script>
</html>
